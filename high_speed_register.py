#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Targon 极速注册机 - 追求绝对速度版本
优化特性：
- 超高并发（可自定义线程数）
- 最小超时时间
- 进度条显示
- 实时性能监控
"""

import time
import threading
import sys
from targon_register import TargonRegister

class HighSpeedRegister(TargonRegister):
    def __init__(self, max_workers=300):
        """极速注册机 - 追求绝对速度"""
        print("🚀 启动极速注册机...")
        print(f"⚡ 并发线程数: {max_workers}")
        print("🎯 目标: 绝对的速度!")
        print("=" * 60)

        super().__init__(max_workers)

        # 性能监控
        self.start_time = None
        self.last_report_time = None
        self.last_success_count = 0
        self.total_target = 0
        self.progress_lock = threading.Lock()
        
    def run_with_monitoring(self, total_count):
        """带实时监控和进度条的高速注册"""
        print(f"🚀 开始极速注册 {total_count} 个账户")
        print(f"⚡ 并发数: {self.max_workers}")
        print(f"🎯 目标: 绝对最快速度!")
        print("=" * 60)

        self.total_target = total_count
        self.start_time = time.time()
        self.last_report_time = self.start_time

        # 启动进度条监控线程
        progress_thread = threading.Thread(target=self._show_progress, daemon=True)
        progress_thread.start()

        # 执行注册
        self.run(total_count)
        
    def _show_progress(self):
        """显示进度条和实时状态"""
        while True:
            time.sleep(2)  # 每2秒更新一次

            with self.progress_lock:
                current_success = self.success_count
                current_failed = self.failed_count
                total_processed = current_success + current_failed

                if self.total_target > 0:
                    # 计算进度
                    progress = total_processed / self.total_target
                    progress_percent = progress * 100

                    # 创建进度条
                    bar_length = 40
                    filled_length = int(bar_length * progress)
                    bar = '█' * filled_length + '░' * (bar_length - filled_length)

                    # 计算速度
                    elapsed = time.time() - self.start_time if self.start_time else 0
                    speed = current_success / elapsed if elapsed > 0 else 0

                    # 估算剩余时间
                    remaining = self.total_target - total_processed
                    eta = remaining / speed if speed > 0 else 0

                    # 清除当前行并显示进度
                    sys.stdout.write('\r')
                    sys.stdout.write(f'📊 [{bar}] {progress_percent:.1f}% | 成功: {current_success}/{self.total_target} | 失败: {current_failed} | 速度: {speed:.2f}/秒 | 剩余: {eta:.0f}秒')
                    sys.stdout.flush()

                    # 如果完成了，跳出循环
                    if total_processed >= self.total_target:
                        print()  # 换行
                        break

def main():
    """极速注册主函数"""
    print("🎯 Targon 极速注册机 - 追求绝对速度版本")
    print("=" * 60)

    try:
        # 获取用户输入
        total_count = int(input("请输入要注册的账户数量: "))

        # 并发数建议
        print("\n⚡ 线程数建议:")
        print("   🔥 极速模式: 300+ (推荐，适合高性能机器)")
        print("   ⚡ 高速模式: 200 (推荐，平衡性能)")
        print("   🚀 标准模式: 100 (稳定，适合普通机器)")
        print("   💡 保守模式: 50 (安全，适合低配机器)")

        max_workers_input = input(f"请输入并发线程数 (默认200): ").strip()
        max_workers = int(max_workers_input) if max_workers_input else 200

        if total_count <= 0:
            print("❌ 账户数量必须大于0")
            return

        if max_workers <= 0:
            print("❌ 并发线程数必须大于0")
            return

        # 根据线程数给出模式提示
        if max_workers >= 300:
            mode_name = "🔥 极速模式"
        elif max_workers >= 200:
            mode_name = "⚡ 高速模式"
        elif max_workers >= 100:
            mode_name = "🚀 标准模式"
        else:
            mode_name = "💡 保守模式"

        print(f"\n🚀 配置确认:")
        print(f"   📊 注册数量: {total_count}")
        print(f"   ⚡ 并发线程: {max_workers}")
        print(f"   🎯 运行模式: {mode_name}")

        confirm = input("\n确认开始注册? (y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 已取消")
            return

        # 创建极速注册机
        register = HighSpeedRegister(max_workers=max_workers)

        # 开始注册
        register.run_with_monitoring(total_count)

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 程序异常: {e}")

if __name__ == "__main__":
    main()
