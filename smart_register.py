#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 智能注册机 - 自动检测验证码服务状态
"""

import time
import threading
import queue
import sys
import random
import string
import requests
from email_utils import create_test_email

class SmartRegister:
    def __init__(self, max_workers=50):
        """智能注册机"""
        self.max_workers = max_workers
        self.task_queue = queue.Queue()
        self.success_count = 0
        self.failed_count = 0
        self.total_tasks = 0
        self.start_time = None
        self.lock = threading.Lock()
        self.captcha_service_available = self.check_captcha_service()
        
        print(f"🧠 智能注册机初始化")
        print(f"⚡ 工作线程: {max_workers}")
        print(f"🔍 验证码服务: {'✅ 可用' if self.captcha_service_available else '❌ 不可用'}")
        
    def check_captcha_service(self):
        """检查验证码服务状态"""
        try:
            response = requests.get("http://127.0.0.1:5000", timeout=2)
            return response.status_code == 200
        except:
            return False
    
    def solve_captcha_smart(self):
        """智能验证码解决"""
        if not self.captcha_service_available:
            # 如果服务不可用，返回None，让调用者知道跳过验证码
            return None
        
        try:
            # 创建验证码任务
            siteurl = "https://targon.com/sign-in?mode=signup"
            sitekey = "0x4AAAAAABneb-oLvPPTgj0A"
            
            task_response = requests.get(
                f"http://127.0.0.1:5000/turnstile?url={siteurl}&sitekey={sitekey}",
                timeout=2
            )
            
            if task_response.status_code != 200:
                return None
                
            task_id = task_response.json().get('task_id')
            if not task_id:
                return None
            
            # 获取验证码结果
            for attempt in range(10):  # 减少等待次数
                try:
                    result_response = requests.get(
                        f"http://127.0.0.1:5000/result?id={task_id}",
                        timeout=1
                    )
                    
                    if result_response.status_code == 200:
                        result = result_response.json()
                        captcha = result.get('value')
                        
                        if captcha and captcha != "CAPTCHA_FAIL":
                            return captcha
                    
                    time.sleep(0.5)  # 快速重试
                    
                except:
                    time.sleep(0.3)
            
            return None
            
        except:
            return None
    
    def generate_password(self):
        """生成随机密码"""
        chars = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(random.choice(chars) for _ in range(16))
    
    def worker(self, worker_id):
        """智能工作线程"""
        while True:
            try:
                task_id = self.task_queue.get_nowait()
                success = self.process_account_smart(worker_id, task_id)
                
                with self.lock:
                    if success:
                        self.success_count += 1
                    else:
                        self.failed_count += 1
                
                self.task_queue.task_done()
                
            except queue.Empty:
                break
            except Exception:
                with self.lock:
                    self.failed_count += 1
                try:
                    self.task_queue.task_done()
                except:
                    pass
    
    def process_account_smart(self, worker_id, task_id):
        """智能处理账户"""
        try:
            # 1. 创建邮箱
            email_result = create_test_email()
            if not email_result:
                return False
            
            email_id, email_address = email_result
            password = self.generate_password()
            
            # 2. 智能处理验证码
            if self.captcha_service_available:
                captcha_token = self.solve_captcha_smart()
                if not captcha_token:
                    print(f"⚠️ 线程 {worker_id} 任务 #{task_id}: 验证码获取失败，跳过")
                    return False
            else:
                # 验证码服务不可用，记录邮箱但不尝试注册
                print(f"📧 线程 {worker_id} 任务 #{task_id}: 邮箱创建成功 {email_address}")
                self.save_email_only(email_address, password)
                return True
            
            # 3. 尝试注册
            session = requests.Session()
            session.headers.update({
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'content-type': 'application/json',
                'origin': 'https://targon.com',
                'referer': 'https://targon.com/sign-in?mode=signup'
            })
            
            register_data = {
                "0": {
                    "json": {
                        "email": email_address,
                        "password": password,
                        "turnstileToken": captcha_token
                    }
                }
            }
            
            response = session.post(
                'https://targon.com/api/trpc/account.createAccount?batch=1',
                json=register_data,
                timeout=8
            )
            
            if response.status_code == 200:
                print(f"✅ 线程 {worker_id} 任务 #{task_id}: 注册成功 {email_address}")
                self.save_account(email_address, password)
                return True
            else:
                print(f"❌ 线程 {worker_id} 任务 #{task_id}: 注册失败 {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 线程 {worker_id} 任务 #{task_id}: 异常 {e}")
            return False
    
    def save_account(self, email, password):
        """保存成功账户"""
        try:
            with self.lock:
                with open("smart_accounts.txt", 'a', encoding='utf-8') as f:
                    f.write(f"{email}:{password}\n")
        except Exception:
            pass
    
    def save_email_only(self, email, password):
        """仅保存邮箱（验证码服务不可用时）"""
        try:
            with self.lock:
                with open("smart_emails_only.txt", 'a', encoding='utf-8') as f:
                    f.write(f"{email}:{password}\n")
        except Exception:
            pass
    
    def show_progress(self):
        """显示进度"""
        while True:
            time.sleep(1)
            
            with self.lock:
                total_processed = self.success_count + self.failed_count
                remaining = self.total_tasks - total_processed
                
                if self.total_tasks > 0:
                    progress = total_processed / self.total_tasks
                    progress_percent = progress * 100
                    
                    # 进度条
                    bar_length = 40
                    filled_length = int(bar_length * progress)
                    bar = '█' * filled_length + '░' * (bar_length - filled_length)
                    
                    # 计算速度
                    elapsed = time.time() - self.start_time if self.start_time else 0
                    speed = self.success_count / elapsed if elapsed > 0 else 0
                    
                    # 显示进度
                    sys.stdout.write('\r')
                    sys.stdout.write(f'🧠 [{bar}] {progress_percent:.1f}% | ✅{self.success_count} ❌{self.failed_count} | 剩余:{remaining} | 速度:{speed:.1f}/秒')
                    sys.stdout.flush()
                    
                    if total_processed >= self.total_tasks:
                        print()
                        break
    
    def run(self, total_count):
        """智能运行"""
        self.total_tasks = total_count
        self.start_time = time.time()
        
        print(f"\n🧠 开始智能注册 {total_count} 个账户")
        print(f"⚡ 工作线程: {self.max_workers}")
        print(f"🔍 验证码服务: {'✅ 可用' if self.captcha_service_available else '❌ 不可用（仅创建邮箱）'}")
        print("=" * 80)
        
        # 填充任务队列
        for i in range(total_count):
            self.task_queue.put(i + 1)
        
        # 启动工作线程
        workers = []
        for i in range(self.max_workers):
            worker = threading.Thread(target=self.worker, args=(i + 1,), daemon=True)
            worker.start()
            workers.append(worker)
        
        # 启动进度监控
        progress_thread = threading.Thread(target=self.show_progress, daemon=True)
        progress_thread.start()
        
        # 等待完成
        self.task_queue.join()
        
        # 最终统计
        elapsed = time.time() - self.start_time
        speed = self.success_count / elapsed if elapsed > 0 else 0
        
        print(f"\n🎯 智能注册完成!")
        print(f"📊 总数: {total_count}")
        print(f"✅ 成功: {self.success_count}")
        print(f"❌ 失败: {self.failed_count}")
        print(f"⏱️ 耗时: {elapsed:.1f}秒")
        print(f"🚀 速度: {speed:.2f}个/秒")
        print(f"📈 成功率: {self.success_count/total_count*100:.1f}%")
        
        if self.captcha_service_available:
            print(f"📄 结果文件: smart_accounts.txt")
        else:
            print(f"📄 邮箱文件: smart_emails_only.txt")
            print(f"💡 提示: 启动Turnstile服务后可完成完整注册")

def main():
    """主函数"""
    print("🧠 智能注册机")
    print("=" * 50)
    print("🔍 自动检测验证码服务状态")
    print("⚡ 服务可用时完整注册")
    print("📧 服务不可用时仅创建邮箱")
    print("=" * 50)
    
    try:
        total_count = int(input("请输入要注册的账户数量: "))
        max_workers_input = input(f"请输入工作线程数 (默认30): ").strip()
        max_workers = int(max_workers_input) if max_workers_input else 30

        if total_count <= 0 or max_workers <= 0:
            print("❌ 数量和线程数必须大于0")
            return

        print(f"\n🧠 智能配置:")
        print(f"   📊 注册数量: {total_count}")
        print(f"   ⚡ 工作线程: {max_workers}")
        print(f"   🎯 模式: 智能适应")
        
        confirm = input("\n确认开始智能注册? (y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 已取消")
            return

        # 创建智能注册机
        register = SmartRegister(max_workers=max_workers)
        
        # 开始智能注册
        register.run(total_count)
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 程序异常: {e}")

if __name__ == "__main__":
    main()
