#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单多线程注册机 - 只走基本流程
"""

import threading
import time
import random
import string
import requests
from email_utils import create_test_email

class SimpleRegister:
    def __init__(self, thread_count=10):
        self.thread_count = thread_count
        self.success_count = 0
        self.failed_count = 0
        self.lock = threading.Lock()
        
    def generate_password(self):
        """生成随机密码"""
        chars = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(random.choice(chars) for _ in range(16))
    
    def solve_captcha(self):
        """解决验证码"""
        try:
            # 创建验证码任务
            siteurl = "https://targon.com/sign-in?mode=signup"
            sitekey = "0x4AAAAAABneb-oLvPPTgj0A"
            
            task_response = requests.get(
                f"http://127.0.0.1:5000/turnstile?url={siteurl}&sitekey={sitekey}",
                timeout=5
            )
            
            if task_response.status_code != 200:
                return None
                
            task_id = task_response.json().get('task_id')
            if not task_id:
                return None
            
            # 获取验证码结果
            for _ in range(20):
                try:
                    result_response = requests.get(
                        f"http://127.0.0.1:5000/result?id={task_id}",
                        timeout=3
                    )
                    
                    if result_response.status_code == 200:
                        result = result_response.json()
                        captcha = result.get('value')
                        
                        if captcha and captcha != "CAPTCHA_FAIL":
                            return captcha
                    
                    time.sleep(2)
                    
                except:
                    time.sleep(1)
            
            return None
            
        except:
            return None
    
    def register_account(self, email, password, captcha_token):
        """注册账户"""
        try:
            session = requests.Session()
            session.headers.update({
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'content-type': 'application/json',
                'origin': 'https://targon.com',
                'referer': 'https://targon.com/sign-in?mode=signup'
            })
            
            register_data = {
                "0": {
                    "json": {
                        "email": email,
                        "password": password,
                        "turnstileToken": captcha_token
                    }
                }
            }
            
            response = session.post(
                'https://targon.com/api/trpc/account.createAccount?batch=1',
                json=register_data,
                timeout=10
            )
            
            return response.status_code == 200
            
        except:
            return False
    
    def save_account(self, email, password):
        """保存账户"""
        with self.lock:
            with open("accounts.txt", 'a', encoding='utf-8') as f:
                f.write(f"{email}:{password}\n")
    
    def worker(self, worker_id, count_per_thread):
        """工作线程"""
        for i in range(count_per_thread):
            try:
                print(f"线程 {worker_id} 开始第 {i+1} 个账户")
                
                # 1. 创建邮箱
                email_result = create_test_email()
                if not email_result:
                    print(f"线程 {worker_id} 邮箱创建失败")
                    with self.lock:
                        self.failed_count += 1
                    continue
                
                email_id, email_address = email_result
                password = self.generate_password()
                print(f"线程 {worker_id} 邮箱创建成功: {email_address}")
                
                # 2. 解决验证码
                captcha_token = self.solve_captcha()
                if not captcha_token:
                    print(f"线程 {worker_id} 验证码解决失败")
                    with self.lock:
                        self.failed_count += 1
                    continue
                
                print(f"线程 {worker_id} 验证码解决成功")
                
                # 3. 注册账户
                if self.register_account(email_address, password, captcha_token):
                    print(f"线程 {worker_id} 注册成功: {email_address}")
                    self.save_account(email_address, password)
                    with self.lock:
                        self.success_count += 1
                else:
                    print(f"线程 {worker_id} 注册失败")
                    with self.lock:
                        self.failed_count += 1
                
            except Exception as e:
                print(f"线程 {worker_id} 异常: {e}")
                with self.lock:
                    self.failed_count += 1
    
    def run(self, total_count):
        """运行注册"""
        print(f"开始注册 {total_count} 个账户，使用 {self.thread_count} 个线程")
        
        # 计算每个线程处理的数量
        count_per_thread = total_count // self.thread_count
        remaining = total_count % self.thread_count
        
        start_time = time.time()
        
        # 创建并启动线程
        threads = []
        for i in range(self.thread_count):
            # 最后一个线程处理剩余的
            thread_count = count_per_thread + (1 if i < remaining else 0)
            if thread_count > 0:
                thread = threading.Thread(
                    target=self.worker, 
                    args=(i+1, thread_count)
                )
                thread.start()
                threads.append(thread)
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 统计结果
        elapsed = time.time() - start_time
        print(f"\n注册完成!")
        print(f"总数: {total_count}")
        print(f"成功: {self.success_count}")
        print(f"失败: {self.failed_count}")
        print(f"耗时: {elapsed:.1f}秒")
        print(f"速度: {self.success_count/elapsed:.2f}个/秒")
        print(f"成功率: {self.success_count/total_count*100:.1f}%")

def main():
    print("简单多线程注册机")
    print("=" * 30)
    
    try:
        total_count = int(input("请输入要注册的账户数量: "))
        thread_count = int(input("请输入线程数量 (默认10): ") or "10")
        
        if total_count <= 0 or thread_count <= 0:
            print("数量和线程数必须大于0")
            return
        
        print(f"\n配置:")
        print(f"注册数量: {total_count}")
        print(f"线程数量: {thread_count}")
        
        confirm = input("\n确认开始? (y/N): ").strip().lower()
        if confirm != 'y':
            print("已取消")
            return
        
        # 创建注册机
        register = SimpleRegister(thread_count)
        
        # 开始注册
        register.run(total_count)
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except ValueError:
        print("请输入有效的数字")
    except Exception as e:
        print(f"程序异常: {e}")

if __name__ == "__main__":
    main()
