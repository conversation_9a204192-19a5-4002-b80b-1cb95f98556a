#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 无验证码测试版 - 测试基础功能
"""

import time
import threading
import queue
import sys
import random
import string
import requests
from email_utils import create_test_email

class NoCaptchaTest:
    def __init__(self, max_workers=20):
        """无验证码测试"""
        self.max_workers = max_workers
        self.task_queue = queue.Queue()
        self.success_count = 0
        self.failed_count = 0
        self.total_tasks = 0
        self.start_time = None
        self.lock = threading.Lock()
        
        print(f"🧪 无验证码测试版初始化")
        print(f"⚡ 工作线程: {max_workers}")
        print(f"🎯 目标: 测试邮箱创建和基础注册")
        
    def generate_password(self):
        """生成随机密码"""
        chars = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(random.choice(chars) for _ in range(16))
    
    def worker(self, worker_id):
        """工作线程"""
        while True:
            try:
                task_id = self.task_queue.get_nowait()
                success = self.test_account_creation(worker_id, task_id)
                
                with self.lock:
                    if success:
                        self.success_count += 1
                        print(f"✅ 线程 {worker_id} 任务 #{task_id} 成功")
                    else:
                        self.failed_count += 1
                        print(f"❌ 线程 {worker_id} 任务 #{task_id} 失败")
                
                self.task_queue.task_done()
                
            except queue.Empty:
                break
            except Exception as e:
                print(f"❌ 线程 {worker_id} 异常: {e}")
                with self.lock:
                    self.failed_count += 1
                try:
                    self.task_queue.task_done()
                except:
                    pass
    
    def test_account_creation(self, worker_id, task_id):
        """测试账户创建流程"""
        try:
            print(f"📧 线程 {worker_id} 任务 #{task_id}: 创建邮箱...")
            
            # 1. 测试邮箱创建
            email_result = create_test_email()
            if not email_result:
                print(f"❌ 线程 {worker_id} 任务 #{task_id}: 邮箱创建失败")
                return False
            
            email_id, email_address = email_result
            password = self.generate_password()
            print(f"✅ 线程 {worker_id} 任务 #{task_id}: 邮箱 {email_address}")
            
            # 2. 测试注册请求（使用假验证码）
            print(f"📝 线程 {worker_id} 任务 #{task_id}: 测试注册请求...")
            
            session = requests.Session()
            session.headers.update({
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'content-type': 'application/json',
                'origin': 'https://targon.com',
                'referer': 'https://targon.com/sign-in?mode=signup'
            })
            
            # 使用假的验证码token进行测试
            fake_captcha = "test_token_" + str(random.randint(100000, 999999))
            
            register_data = {
                "0": {
                    "json": {
                        "email": email_address,
                        "password": password,
                        "turnstileToken": fake_captcha
                    }
                }
            }
            
            try:
                response = session.post(
                    'https://targon.com/api/trpc/account.createAccount?batch=1',
                    json=register_data,
                    timeout=5
                )
                
                print(f"📊 线程 {worker_id} 任务 #{task_id}: 响应状态 {response.status_code}")
                print(f"📊 线程 {worker_id} 任务 #{task_id}: 响应内容 {response.text[:100]}...")
                
                # 保存测试结果
                self.save_test_result(email_address, password, response.status_code, response.text[:200])
                
                # 即使验证码失败，我们也认为测试成功（因为邮箱创建成功了）
                return True
                
            except Exception as e:
                print(f"❌ 线程 {worker_id} 任务 #{task_id}: 注册请求异常 {e}")
                return False
                
        except Exception as e:
            print(f"❌ 线程 {worker_id} 任务 #{task_id}: 处理异常 {e}")
            return False
    
    def save_test_result(self, email, password, status_code, response_text):
        """保存测试结果"""
        try:
            with self.lock:
                with open("test_results.txt", 'a', encoding='utf-8') as f:
                    f.write(f"{email}:{password}|{status_code}|{response_text}\n")
        except Exception:
            pass
    
    def show_progress(self):
        """显示进度"""
        while True:
            time.sleep(2)
            
            with self.lock:
                total_processed = self.success_count + self.failed_count
                remaining = self.total_tasks - total_processed
                
                if self.total_tasks > 0:
                    progress = total_processed / self.total_tasks
                    progress_percent = progress * 100
                    
                    # 进度条
                    bar_length = 30
                    filled_length = int(bar_length * progress)
                    bar = '█' * filled_length + '░' * (bar_length - filled_length)
                    
                    # 计算速度
                    elapsed = time.time() - self.start_time if self.start_time else 0
                    speed = self.success_count / elapsed if elapsed > 0 else 0
                    
                    # 显示进度
                    sys.stdout.write('\r')
                    sys.stdout.write(f'🧪 [{bar}] {progress_percent:.1f}% | ✅{self.success_count} ❌{self.failed_count} | 剩余:{remaining} | 速度:{speed:.1f}/秒')
                    sys.stdout.flush()
                    
                    if total_processed >= self.total_tasks:
                        print()
                        break
    
    def run(self, total_count):
        """运行测试"""
        self.total_tasks = total_count
        self.start_time = time.time()
        
        print(f"\n🧪 开始无验证码测试 {total_count} 个账户")
        print(f"⚡ 工作线程: {self.max_workers}")
        print(f"🎯 目标: 测试邮箱创建速度")
        print("=" * 60)
        
        # 填充任务队列
        for i in range(total_count):
            self.task_queue.put(i + 1)
        
        # 启动工作线程
        workers = []
        for i in range(self.max_workers):
            worker = threading.Thread(target=self.worker, args=(i + 1,), daemon=True)
            worker.start()
            workers.append(worker)
        
        # 启动进度监控
        progress_thread = threading.Thread(target=self.show_progress, daemon=True)
        progress_thread.start()
        
        # 等待完成
        self.task_queue.join()
        
        # 最终统计
        elapsed = time.time() - self.start_time
        speed = self.success_count / elapsed if elapsed > 0 else 0
        
        print(f"\n🎯 测试完成!")
        print(f"📊 总数: {total_count}")
        print(f"✅ 成功: {self.success_count}")
        print(f"❌ 失败: {self.failed_count}")
        print(f"⏱️ 耗时: {elapsed:.1f}秒")
        print(f"🚀 速度: {speed:.2f}个/秒")
        print(f"📈 成功率: {self.success_count/total_count*100:.1f}%")
        print(f"📄 结果文件: test_results.txt")

def main():
    """主函数"""
    print("🧪 无验证码测试工具")
    print("=" * 40)
    print("🎯 用于测试邮箱创建和基础注册速度")
    print("⚡ 不依赖Turnstile验证码服务")
    print("=" * 40)
    
    try:
        total_count = int(input("请输入测试数量 (建议5-10): "))
        max_workers_input = input(f"请输入工作线程数 (默认10): ").strip()
        max_workers = int(max_workers_input) if max_workers_input else 10

        if total_count <= 0 or max_workers <= 0:
            print("❌ 数量和线程数必须大于0")
            return

        print(f"\n🧪 测试配置:")
        print(f"   📊 测试数量: {total_count}")
        print(f"   ⚡ 工作线程: {max_workers}")
        print(f"   🎯 模式: 无验证码测试")
        
        confirm = input("\n确认开始测试? (y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 已取消")
            return

        # 创建测试器
        tester = NoCaptchaTest(max_workers=max_workers)
        
        # 开始测试
        tester.run(total_count)
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 程序异常: {e}")

if __name__ == "__main__":
    main()
