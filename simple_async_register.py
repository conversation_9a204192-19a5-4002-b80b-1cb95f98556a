#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版异步注册机 - 专注核心功能
"""

import time
import threading
import queue
import sys
import random
import string
import requests
from email_utils import create_test_email, fetch_first_email, extract_verification_token
from turnstile_utils import solve_turnstile_captcha

class SimpleAsyncRegister:
    def __init__(self, max_workers=50):
        """简化异步注册机"""
        self.max_workers = max_workers
        self.task_queue = queue.Queue()
        self.success_count = 0
        self.failed_count = 0
        self.total_tasks = 0
        self.start_time = None
        self.lock = threading.Lock()
        self.worker_status = {}
        
        print(f"🚀 简化异步注册机初始化")
        print(f"⚡ 工作线程: {max_workers}")
        
    def generate_password(self):
        """生成随机密码"""
        chars = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(random.choice(chars) for _ in range(16))
    
    def worker(self, worker_id):
        """工作线程"""
        print(f"🚀 线程 {worker_id} 启动")
        
        while True:
            try:
                # 获取任务
                task_id = self.task_queue.get_nowait()
                print(f"⚡ 线程 {worker_id} 开始处理任务 #{task_id}")
                
                # 处理任务
                success = self.process_account(worker_id, task_id)
                
                # 更新计数
                with self.lock:
                    if success:
                        self.success_count += 1
                        print(f"✅ 线程 {worker_id} 完成任务 #{task_id}")
                    else:
                        self.failed_count += 1
                        print(f"❌ 线程 {worker_id} 任务 #{task_id} 失败")
                
                self.task_queue.task_done()
                
            except queue.Empty:
                print(f"💤 线程 {worker_id} 没有更多任务")
                break
            except Exception as e:
                print(f"❌ 线程 {worker_id} 异常: {e}")
                with self.lock:
                    self.failed_count += 1
                try:
                    self.task_queue.task_done()
                except:
                    pass
    
    def process_account(self, worker_id, task_id):
        """处理单个账户注册"""
        try:
            print(f"📧 线程 {worker_id} 任务 #{task_id}: 创建邮箱...")
            
            # 1. 创建邮箱
            email_result = create_test_email()
            if not email_result:
                print(f"❌ 线程 {worker_id} 任务 #{task_id}: 邮箱创建失败")
                return False
            
            email_id, email_address = email_result
            password = self.generate_password()
            print(f"✅ 线程 {worker_id} 任务 #{task_id}: 邮箱创建成功 {email_address}")
            
            print(f"🔐 线程 {worker_id} 任务 #{task_id}: 解决验证码...")
            
            # 2. 解决验证码
            captcha_token = solve_turnstile_captcha()
            if not captcha_token:
                print(f"❌ 线程 {worker_id} 任务 #{task_id}: 验证码解决失败")
                return False
            
            print(f"✅ 线程 {worker_id} 任务 #{task_id}: 验证码解决成功")
            
            print(f"📝 线程 {worker_id} 任务 #{task_id}: 注册账户...")
            
            # 3. 注册账户
            success = self.register_account(email_address, password, captcha_token)
            if success:
                print(f"✅ 线程 {worker_id} 任务 #{task_id}: 注册成功!")
                
                # 保存账户信息
                self.save_account(email_address, password)
                return True
            else:
                print(f"❌ 线程 {worker_id} 任务 #{task_id}: 注册失败")
                return False
                
        except Exception as e:
            print(f"❌ 线程 {worker_id} 任务 #{task_id}: 处理异常 {e}")
            return False
    
    def register_account(self, email, password, captcha_token):
        """注册账户"""
        try:
            session = requests.Session()
            session.headers.update({
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'content-type': 'application/json',
                'origin': 'https://targon.com',
                'referer': 'https://targon.com/sign-in?mode=signup'
            })
            
            register_data = {
                "0": {
                    "json": {
                        "email": email,
                        "password": password,
                        "turnstileToken": captcha_token
                    }
                }
            }
            
            response = session.post(
                'https://targon.com/api/trpc/account.createAccount?batch=1',
                json=register_data,
                timeout=10
            )
            
            return response.status_code == 200
            
        except Exception as e:
            print(f"❌ 注册请求异常: {e}")
            return False
    
    def save_account(self, email, password):
        """保存账户信息"""
        try:
            account_info = f"{email}:{password}\n"
            with open("simple_accounts.txt", 'a', encoding='utf-8') as f:
                f.write(account_info)
        except Exception:
            pass
    
    def show_progress(self):
        """显示进度"""
        while True:
            time.sleep(2)
            
            with self.lock:
                total_processed = self.success_count + self.failed_count
                remaining = self.total_tasks - total_processed
                
                if self.total_tasks > 0:
                    progress = total_processed / self.total_tasks
                    progress_percent = progress * 100
                    
                    # 进度条
                    bar_length = 30
                    filled_length = int(bar_length * progress)
                    bar = '█' * filled_length + '░' * (bar_length - filled_length)
                    
                    # 计算速度
                    elapsed = time.time() - self.start_time if self.start_time else 0
                    speed = self.success_count / elapsed if elapsed > 0 else 0
                    
                    # 显示进度
                    sys.stdout.write('\r')
                    sys.stdout.write(f'📊 [{bar}] {progress_percent:.1f}% | ✅{self.success_count} ❌{self.failed_count} | 剩余:{remaining} | 速度:{speed:.1f}/秒')
                    sys.stdout.flush()
                    
                    # 完成检查
                    if total_processed >= self.total_tasks:
                        print()
                        break
    
    def run(self, total_count):
        """运行注册"""
        self.total_tasks = total_count
        self.start_time = time.time()
        
        print(f"\n🚀 开始简化异步注册 {total_count} 个账户")
        print(f"⚡ 工作线程: {self.max_workers}")
        print("=" * 60)
        
        # 填充任务队列
        for i in range(total_count):
            self.task_queue.put(i + 1)
        
        # 启动工作线程
        workers = []
        for i in range(self.max_workers):
            worker = threading.Thread(target=self.worker, args=(i + 1,), daemon=True)
            worker.start()
            workers.append(worker)
        
        # 启动进度监控
        progress_thread = threading.Thread(target=self.show_progress, daemon=True)
        progress_thread.start()
        
        # 等待所有任务完成
        self.task_queue.join()
        
        # 最终统计
        elapsed = time.time() - self.start_time
        speed = self.success_count / elapsed if elapsed > 0 else 0
        
        print(f"\n🎯 注册完成!")
        print(f"📊 总数: {total_count}")
        print(f"✅ 成功: {self.success_count}")
        print(f"❌ 失败: {self.failed_count}")
        print(f"⏱️ 耗时: {elapsed:.1f}秒")
        print(f"🚀 速度: {speed:.2f}个/秒")
        print(f"📈 成功率: {self.success_count/total_count*100:.1f}%")

def main():
    """主函数"""
    print("🎯 简化异步注册机")
    print("=" * 40)
    
    try:
        total_count = int(input("请输入要注册的账户数量: "))
        max_workers_input = input(f"请输入工作线程数 (默认20): ").strip()
        max_workers = int(max_workers_input) if max_workers_input else 20

        if total_count <= 0 or max_workers <= 0:
            print("❌ 数量和线程数必须大于0")
            return

        print(f"\n🚀 配置:")
        print(f"   📊 注册数量: {total_count}")
        print(f"   ⚡ 工作线程: {max_workers}")
        
        confirm = input("\n确认开始? (y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 已取消")
            return

        # 创建注册机
        register = SimpleAsyncRegister(max_workers=max_workers)
        
        # 开始注册
        register.run(total_count)
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 程序异常: {e}")

if __name__ == "__main__":
    main()
