@echo off
chcp 65001 >nul
title Targon Async Register

echo.
echo === Targon Async Register ===
echo Mode: Async Workers
echo Feature: Independent Threads
echo Advantage: Fast Workers Do More
echo Display: Real-time Progress
echo =============================
echo.

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found
    pause
    exit /b 1
)

REM Check files
if not exist "async_targon_register.py" (
    echo ERROR: async_targon_register.py not found
    pause
    exit /b 1
)

if not exist "turnstile_utils.py" (
    echo ERROR: turnstile_utils.py not found
    pause
    exit /b 1
)

if not exist "email_utils.py" (
    echo ERROR: email_utils.py not found
    pause
    exit /b 1
)

REM Check Turnstile service
echo Checking Turnstile service...
curl -s http://127.0.0.1:5000 >nul 2>&1
if errorlevel 1 (
    echo WARNING: Turnstile service not running
    echo Please start Turnstile-Solver service first
    echo.
    set /p choice="Continue anyway? (y/N): "
    if /i not "%choice%"=="y" (
        echo Cancelled
        pause
        exit /b 1
    )
) else (
    echo Turnstile service is running
)

echo.
echo Starting async register...
echo Preparing async worker mode...
echo.

REM Run async register
python async_targon_register.py

echo.
echo Registration completed!
echo Result files:
echo    - targon_accounts.txt
echo    - targon_apikeys.txt
echo.
pause
