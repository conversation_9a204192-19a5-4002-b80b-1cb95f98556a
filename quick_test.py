#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Targon 快速测试脚本 - 解决等待时间过长问题
"""

import time
from high_speed_register import HighSpeedRegister

def quick_single_test():
    """快速单账户测试"""
    print("⚡ 快速单账户测试")
    print("=" * 40)
    
    # 使用较少的线程数进行快速测试
    register = HighSpeedRegister(max_workers=50)
    
    start_time = time.time()
    register.run_with_monitoring(1)
    end_time = time.time()
    
    elapsed = end_time - start_time
    
    print(f"\n📊 测试结果:")
    print(f"   耗时: {elapsed:.1f}秒")
    print(f"   成功: {register.success_count}/1")
    print(f"   失败: {register.failed_count}/1")
    print(f"   成功率: {register.success_count * 100:.1f}%")

def quick_batch_test():
    """快速批量测试"""
    print("⚡ 快速批量测试")
    print("=" * 40)
    
    try:
        count = int(input("请输入测试数量 (建议5-10): "))
        threads = int(input("请输入线程数 (建议50-100): ") or "50")
        
        if count <= 0 or threads <= 0:
            print("❌ 数量和线程数必须大于0")
            return
            
        print(f"\n🚀 开始测试 {count} 个账户，线程数: {threads}")
        
        register = HighSpeedRegister(max_workers=threads)
        
        start_time = time.time()
        register.run_with_monitoring(count)
        end_time = time.time()
        
        elapsed = end_time - start_time
        speed = register.success_count / elapsed if elapsed > 0 else 0
        
        print(f"\n📊 测试结果:")
        print(f"   总数: {count}")
        print(f"   成功: {register.success_count}")
        print(f"   失败: {register.failed_count}")
        print(f"   耗时: {elapsed:.1f}秒")
        print(f"   速度: {speed:.2f}个/秒")
        print(f"   成功率: {register.success_count/count*100:.1f}%")
        
    except ValueError:
        print("❌ 请输入有效数字")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def main():
    """主函数"""
    print("🎯 Targon 快速测试工具")
    print("=" * 40)
    
    print("选择测试模式:")
    print("1. 单账户测试 (最快)")
    print("2. 批量测试 (自定义)")
    print("3. 退出")
    
    try:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            quick_single_test()
        elif choice == "2":
            quick_batch_test()
        elif choice == "3":
            print("👋 再见!")
            return
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    except Exception as e:
        print(f"❌ 异常: {e}")

if __name__ == "__main__":
    main()
