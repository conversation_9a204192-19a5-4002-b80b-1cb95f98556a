#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Turnstile验证码集成
"""

from targon_register import TargonRegister
from email_utils import create_test_email

def test_turnstile_integration():
    """测试Turnstile验证码集成"""
    print("🚀 开始测试Turnstile验证码集成...")
    
    # 创建注册机实例
    register = TargonRegister(max_workers=1)
    
    # 测试验证码解决
    print("\n📋 测试验证码解决...")
    captcha_token = register.solve_turnstile_captcha()
    
    if captcha_token:
        print(f"✅ 验证码解决成功: {captcha_token[:50]}...")
        
        # 创建测试邮箱
        print("\n📧 创建测试邮箱...")
        email_result = create_test_email()
        if email_result and len(email_result) == 2:
            email_id, email_address = email_result  # 注意顺序：函数返回(id, email)
            print(f"✅ 邮箱创建成功: {email_address}")
            print(f"📧 邮箱ID: {email_id}")

            # 测试注册
            print(f"\n🔐 测试注册账户: {email_address}")
            password = register.generate_password()
            session, success = register.register_account(email_address, password)
            
            if success:
                print(f"✅ 注册测试成功!")
                print(f"📧 邮箱: {email_address}")
                print(f"🔑 密码: {password}")
            else:
                print(f"❌ 注册测试失败")
        else:
            print("❌ 邮箱创建失败")
    else:
        print("❌ 验证码解决失败")

if __name__ == "__main__":
    test_turnstile_integration()
