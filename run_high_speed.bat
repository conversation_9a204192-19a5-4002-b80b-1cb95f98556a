@echo off
chcp 65001 >nul
title Targon 极速注册机 - 追求绝对速度

echo.
echo ⚡⚡⚡ Targon 极速注册机 ⚡⚡⚡
echo ================================
echo 🎯 目标: 绝对的速度!
echo 🚀 特性: 进度条 + 自定义线程
echo ⚡ 优化: 减少等待时间
echo 📊 显示: 实时进度和成功率
echo ================================
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 请确保已安装Python并添加到PATH
    pause
    exit /b 1
)

REM 检查依赖文件
if not exist "high_speed_register.py" (
    echo ❌ 错误: 未找到 high_speed_register.py
    pause
    exit /b 1
)

if not exist "targon_register.py" (
    echo ❌ 错误: 未找到 targon_register.py
    pause
    exit /b 1
)

if not exist "turnstile_utils.py" (
    echo ❌ 错误: 未找到 turnstile_utils.py
    pause
    exit /b 1
)

if not exist "email_utils.py" (
    echo ❌ 错误: 未找到 email_utils.py
    pause
    exit /b 1
)

REM 检查Turnstile服务
echo 🔍 检查Turnstile验证码服务...
curl -s http://127.0.0.1:5000 >nul 2>&1
if errorlevel 1 (
    echo ⚠️  警告: Turnstile服务未运行
    echo 请先启动 Turnstile-Solver 服务
    echo.
    set /p choice="是否继续? (y/N): "
    if /i not "%choice%"=="y" (
        echo ❌ 已取消
        pause
        exit /b 1
    )
) else (
    echo ✅ Turnstile服务正常运行
)

echo.
echo 🚀 启动极速注册机...
echo ⚡ 准备进入极速模式...
echo.

REM 运行极速注册机
python high_speed_register.py

echo.
echo 📊 注册完成!
echo 📄 结果文件:
echo    - targon_accounts.txt (完整账户信息)
echo    - targon_apikeys.txt (API密钥)
echo.
pause
