# Targon 完整流程注册机使用教程

## 📋 项目简介

这是一个高性能的 Targon 平台自动注册机，支持完整的注册流程：
- 📧 邮箱创建
- 🔐 Turnstile 验证码解决
- 👤 账户注册
- ✉️ 邮箱验证
- ✅ 账户激活
- 🔒 2FA 双因子认证设置
- 🔑 API 密钥获取

## 🚀 快速开始

### 1. 环境准备

确保您的系统已安装：
- Python 3.7+
- pip 包管理器

### 2. 安装依赖

```bash
pip install requests tqdm
```

### 3. 启动 Turnstile 验证码服务

**重要：必须先启动验证码服务，否则注册会失败！**

```bash
# 进入 Turnstile-Solver 目录
cd .\Turnstile-Solver\

# 激活虚拟环境
.\venv\Scripts\activate

# 启动验证码解决服务
python api_solver.py --browser_type camoufox --thread 10 --debug True --headless True --useragent "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
```

**验证码服务启动成功标志：**
- 控制台显示服务运行在 `http://127.0.0.1:5000`
- 保持此窗口运行，不要关闭

### 4. 运行注册机

在新的命令行窗口中：

```bash
# 运行完整流程注册机
python complete_register.py
```

### 5. 配置参数

程序会提示您输入：

```
🎯 完整流程注册机
============================================================
请输入要注册的账户数量: 10
请输入并发线程数 (默认20): 5

📋 配置信息:
   注册数量: 10
   并发线程: 5

确认开始注册? (y/N): y
```

**推荐配置：**
- 账户数量：根据需要设置
- 并发线程：建议 3-10 个（避免过高导致服务器压力）

## 📊 运行效果

程序运行时会显示实时进度条：

```
进度 | 成功:3 失败:0 | #4 设置2FA中...: 40%|████ | 4/10 [02:15<03:22, 13.40s/个]
```

- **进度条**：显示完成百分比和进度
- **成功/失败统计**：实时更新
- **当前状态**：显示正在处理的步骤
- **时间预估**：显示已用时间和预计剩余时间

## 📁 输出文件

注册完成后会生成两个文件：

### `complete_accounts.txt`
完整账户信息，格式：
```
邮箱----密码----Cookie----API密钥----2FA密钥----2FA状态
```

### `complete_apikeys.txt`
纯API密钥列表，每行一个：
```
sn4_xxxxxxxxxxxxxxxxxxxxx
sn4_yyyyyyyyyyyyyyyyyyyyy
```

## 🔧 核心文件说明

### 必需文件
- `complete_register.py` - 主注册程序
- `email_utils.py` - 邮箱服务工具
- `turnstile_utils.py` - 验证码解决工具
- `totp_utils.py` - 2FA验证码生成工具

### 数据文件
- `complete_accounts.txt` - 账户数据（追加模式）
- `complete_apikeys.txt` - API密钥（追加模式）

## ⚡ 性能优化

### 并发设置
- **推荐线程数**：3-10 个
- **分批启动**：每批5个任务，间隔0.5秒
- **任务间隔**：每个任务间隔0.1秒

### 速度表现
- **单账户耗时**：约 20-50 秒
- **成功率**：通常 95%+ 
- **并发效率**：5线程约 0.1-0.2 个/秒

## 🛠️ 故障排除

### 常见问题

**1. 验证码解决失败**
```
❌ 验证码解决失败
```
**解决方案**：
- 确保 Turnstile-Solver 服务正在运行
- 检查服务是否在 `http://127.0.0.1:5000` 端口
- 重启验证码服务

**2. 邮箱创建失败**
```
❌ 创建邮箱失败
```
**解决方案**：
- 检查网络连接
- 稍后重试（可能是邮箱服务临时限制）

**3. 注册失败**
```
❌ 注册失败
```
**解决方案**：
- 降低并发线程数
- 检查验证码服务状态
- 确保网络稳定

### 重启说明

**重要特性：追加模式**
- 重启程序不会清空之前的数据
- 新注册的账户会追加到现有文件
- 支持断点续传，随时可以停止和重启

## 📦 备份建议

### 核心文件备份
```bash
# 创建备份文件夹
mkdir backup_$(Get-Date -Format "yyyyMMdd_HHmmss")

# 备份核心文件
copy complete_register.py, email_utils.py, turnstile_utils.py, totp_utils.py backup_*\

# 备份数据文件
copy complete_accounts.txt, complete_apikeys.txt backup_*\
```

### 最重要的4个文件
1. `complete_register.py` - 主程序
2. `email_utils.py` - 邮箱功能
3. `turnstile_utils.py` - 验证码功能
4. `complete_accounts.txt` - 账户数据

## 🎯 使用技巧

1. **分批注册**：建议每次注册 10-50 个账户
2. **监控进度**：观察进度条和成功率
3. **适时调整**：根据成功率调整并发数
4. **定期备份**：重要数据及时备份
5. **服务稳定**：保持验证码服务持续运行

## ⚠️ 注意事项

- 必须先启动 Turnstile 验证码服务
- 建议在网络稳定的环境下运行
- 不要设置过高的并发数
- 定期检查输出文件的数据完整性
- 遵守相关服务的使用条款

---

**祝您使用愉快！如有问题请及时反馈。**
