import requests, random, string, re, time
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 新邮箱API配置
API_BASE_URL = "https://email.millx.bio"
API_KEY = "mk_KoO2qR9lSR89q4QpxE7UW6UNic2ktxGO"

def create_robust_session():
    """创建具有重试机制的会话"""
    session = requests.Session()

    # 配置重试策略
    retry_strategy = Retry(
        total=5,  # 总重试次数
        backoff_factor=1,  # 退避因子
        status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的状态码
        allowed_methods=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"]
    )

    # 配置适配器
    adapter = HTTPAdapter(
        max_retries=retry_strategy,
        pool_connections=10,
        pool_maxsize=20
    )

    session.mount("http://", adapter)
    session.mount("https://", adapter)

    # 设置默认超时和头部
    session.timeout = 15
    session.verify = False  # 暂时禁用SSL验证以避免SSL错误

    return session

def generate_random_name():
    """生成随机邮箱名称 - 更长的名称避免冲突"""
    letters1 = ''.join(random.choices(string.ascii_lowercase, k=8))
    numbers = ''.join(random.choices(string.digits, k=8))
    letters2 = ''.join(random.choices(string.ascii_lowercase, k=6))
    letters3 = ''.join(random.choices(string.ascii_lowercase, k=4))
    return letters1 + numbers + letters2 + letters3

def create_test_email():
    """创建邮箱，返回邮箱ID和邮箱地址"""
    session = create_robust_session()

    for attempt in range(5):  # 增加重试次数
        try:
            print(f"🔄 尝试创建邮箱 (第{attempt+1}次)...")
            random_name = generate_random_name()

            # 添加请求间隔，避免过于频繁
            if attempt > 0:
                wait_time = min(2 ** attempt, 10)  # 指数退避，最大10秒
                print(f"⏳ 等待{wait_time}秒后重试...")
                time.sleep(wait_time)

            res = session.post(
                f"{API_BASE_URL}/api/emails/generate",
                json={
                    "name": random_name,
                    "expiryTime": 3600000,  # 1小时有效期
                    "domain": "millx.bio"
                },
                headers={
                    "X-API-Key": API_KEY,
                    "Content-Type": "application/json",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                },
                timeout=15
            )

            if res.status_code == 200:
                try:
                    data = res.json()
                    email_id = data.get('id')
                    email_address = data.get('email')
                    if email_id and email_address:
                        print(f"✅ 邮箱创建成功: {email_address}")
                        return email_id, email_address
                    else:
                        print(f"❌ 邮箱数据不完整: {data}")
                        continue
                except ValueError as e:
                    print(f"❌ JSON解析失败: {e}, 响应内容: {res.text[:100]}")
                    continue
            else:
                print(f"❌ 创建邮箱失败: {res.status_code}")
                if res.text:
                    print(f"   响应内容: {res.text[:200]}")
                continue

        except requests.exceptions.SSLError as e:
            print(f"❌ SSL连接错误 (第{attempt+1}次): {e}")
            continue
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 连接错误 (第{attempt+1}次): {e}")
            continue
        except requests.exceptions.Timeout as e:
            print(f"❌ 请求超时 (第{attempt+1}次): {e}")
            continue
        except Exception as e:
            print(f"❌ 创建邮箱异常 (第{attempt+1}次): {e}")
            continue

    print("❌ 创建邮箱最终失败，已达到最大重试次数")
    return None

def fetch_first_email(email_id):
    """使用邮箱ID获取邮件列表中的第一个邮件内容"""
    session = create_robust_session()

    for attempt in range(3):
        try:
            if attempt > 0:
                time.sleep(2)  # 重试间隔

            res = session.get(
                f"{API_BASE_URL}/api/emails/{email_id}",
                headers={
                    "X-API-Key": API_KEY,
                    "Content-Type": "application/json",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                },
                timeout=10
            )

            if res.status_code == 200:
                try:
                    data = res.json()
                    if data.get("messages") and len(data["messages"]) > 0:
                        # 获取第一封邮件的ID
                        message_id = data["messages"][0]["id"]

                        # 获取具体邮件内容
                        message_res = session.get(
                            f"{API_BASE_URL}/api/emails/{email_id}/{message_id}",
                            headers={
                                "X-API-Key": API_KEY,
                                "Content-Type": "application/json",
                                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                            },
                            timeout=10
                        )

                        if message_res.status_code == 200:
                            try:
                                message_data = message_res.json()
                                # 从message字段中获取内容
                                message_content = message_data.get("message", {})
                                return message_content.get("html") or message_content.get("content") or message_content.get("text")
                            except ValueError as e:
                                print(f"获取邮件内容JSON解析失败: {e}")
                                continue
                        else:
                            print(f"获取邮件内容失败: {message_res.status_code}")
                            continue
                    return None
                except ValueError as e:
                    print(f"获取邮件列表JSON解析失败: {e}")
                    continue
            else:
                print(f"获取邮件列表失败: {res.status_code}")
                continue

        except requests.exceptions.SSLError as e:
            print(f"获取邮件SSL错误: {e}")
            continue
        except requests.exceptions.ConnectionError as e:
            print(f"获取邮件连接错误: {e}")
            continue
        except Exception as e:
            print(f"获取邮件异常: {e}")
            continue

    return None

def extract_verification_token(email_content):
    """从Friendli.ai邮件内容中提取验证token"""
    if not email_content:
        return None

    import re

    # 提取验证链接中的token
    patterns = [
        # Friendli.ai验证链接格式
        r"verify-email/callback\?token=([A-Za-z0-9]+)",
        # 备用格式
        r"token=([A-Za-z0-9]+)",
    ]

    for i, pattern in enumerate(patterns):
        match = re.search(pattern, email_content, re.IGNORECASE)
        if match:
            token = match.group(1)
            print(f"使用模式{i+1}提取验证token: {token[:20]}...")
            return token

    return None

def extract_verification_code(email_content):
    """从邮件内容中提取验证码（保留兼容性）"""
    if not email_content:
        return None

    import re

    # 多种验证码格式
    patterns = [
        r">([A-Z0-9]{3}-[A-Z0-9]{3})<",  # 标准格式 >ABC-123<
        r"([A-Z]{3}-[A-Z]{3})",          # E2G-CMD 格式
        r"([A-Z]{3}-[A-Z0-9]{3})",       # BKG-6EO 格式
        r"([A-Z0-9]{3}-[A-Z]{3})",       # 123-ABC 格式
        r"([A-Z]{3}-[0-9]{3})",          # ABC-123 格式
        r"([0-9]{3}-[A-Z]{3})",          # 123-ABC 格式
        r"([A-Z0-9]{6})",                # 6位无分隔符
        r"验证码[：:]\s*([A-Z0-9-]+)",    # 中文标识
        r"code[：:]\s*([A-Z0-9-]+)",     # 英文标识
        r"verification[：:]\s*([A-Z0-9-]+)",  # 英文verification
        # 在HTML表格中的验证码
        r"<td[^>]*>([A-Z0-9]{3}-[A-Z0-9]{3})</td>",  # 表格中的验证码
        r"bold;\">([A-Z0-9]{3}-[A-Z0-9]{3})</td>",   # 粗体表格中的验证码
    ]

    for i, pattern in enumerate(patterns):
        match = re.search(pattern, email_content, re.IGNORECASE)
        if match:
            verify_code = match.group(1).replace("-", "")
            print(f"使用模式{i+1}提取验证码: {verify_code}")
            return verify_code

    return None