import requests, random, string, re

# 新邮箱API配置
API_BASE_URL = "https://email.millx.bio"
API_KEY = "mk_KoO2qR9lSR89q4QpxE7UW6UNic2ktxGO"

def generate_random_name():
    """生成随机邮箱名称 - 更长的名称避免冲突"""
    letters1 = ''.join(random.choices(string.ascii_lowercase, k=8))
    numbers = ''.join(random.choices(string.digits, k=8))
    letters2 = ''.join(random.choices(string.ascii_lowercase, k=6))
    letters3 = ''.join(random.choices(string.ascii_lowercase, k=4))
    return letters1 + numbers + letters2 + letters3

def create_test_email():
    """创建邮箱，返回邮箱ID和邮箱地址"""
    import time

    for attempt in range(3):  # 最多重试3次
        try:
            print(f"🔄 尝试创建邮箱 (第{attempt+1}次)...")
            random_name = generate_random_name()
            res = requests.post(
                f"{API_BASE_URL}/api/emails/generate",
                json={
                    "name": random_name,
                    "expiryTime": 3600000,  # 1小时有效期
                    "domain": "millx.bio"
                },
                headers={
                    "X-API-Key": API_KEY,
                    "Content-Type": "application/json"
                },
                timeout=8  # 恢复稳定超时，避免过多失败
            )
            if res.status_code == 200:
                data = res.json()
                print(f"✅ 邮箱创建成功: {data.get('email')}")
                # 返回邮箱ID和邮箱地址
                return data.get('id'), data.get('email')
            else:
                print(f"❌ 创建邮箱失败: {res.status_code}, {res.text}")
                if attempt < 2:  # 不是最后一次尝试
                    print(f"⏳ 等待3秒后重试...")
                    time.sleep(3)
                    continue
                return None, None
        except Exception as e:
            print(f"❌ 创建邮箱异常: {e}")
            if attempt < 2:  # 不是最后一次尝试
                print(f"⏳ 等待3秒后重试...")
                time.sleep(3)
                continue
            return None, None

    return None, None

def fetch_first_email(email_id):
    """使用邮箱ID获取邮件列表中的第一个邮件内容"""
    try:
        res = requests.get(
            f"{API_BASE_URL}/api/emails/{email_id}",
            headers={
                "X-API-Key": API_KEY,
                "Content-Type": "application/json"
            }
        )

        if res.status_code == 200:
            data = res.json()
            if data.get("messages") and len(data["messages"]) > 0:
                # 获取第一封邮件的ID
                message_id = data["messages"][0]["id"]

                # 获取具体邮件内容
                message_res = requests.get(
                    f"{API_BASE_URL}/api/emails/{email_id}/{message_id}",
                    headers={
                        "X-API-Key": API_KEY,
                        "Content-Type": "application/json"
                    }
                )

                if message_res.status_code == 200:
                    message_data = message_res.json()
                    # 从message字段中获取内容
                    message_content = message_data.get("message", {})
                    return message_content.get("html") or message_content.get("content") or message_content.get("text")
                else:
                    print(f"获取邮件内容失败: {message_res.status_code}")
                    return None
            return None
        else:
            print(f"获取邮件列表失败: {res.status_code}")
            return None
    except Exception as e:
        print(f"获取邮件异常: {e}")
        return None

def extract_verification_token(email_content):
    """从Friendli.ai邮件内容中提取验证token"""
    if not email_content:
        return None

    import re

    # 提取验证链接中的token
    patterns = [
        # Friendli.ai验证链接格式
        r"verify-email/callback\?token=([A-Za-z0-9]+)",
        # 备用格式
        r"token=([A-Za-z0-9]+)",
    ]

    for i, pattern in enumerate(patterns):
        match = re.search(pattern, email_content, re.IGNORECASE)
        if match:
            token = match.group(1)
            print(f"使用模式{i+1}提取验证token: {token[:20]}...")
            return token

    return None

def extract_verification_code(email_content):
    """从邮件内容中提取验证码（保留兼容性）"""
    if not email_content:
        return None

    import re

    # 多种验证码格式
    patterns = [
        r">([A-Z0-9]{3}-[A-Z0-9]{3})<",  # 标准格式 >ABC-123<
        r"([A-Z]{3}-[A-Z]{3})",          # E2G-CMD 格式
        r"([A-Z]{3}-[A-Z0-9]{3})",       # BKG-6EO 格式
        r"([A-Z0-9]{3}-[A-Z]{3})",       # 123-ABC 格式
        r"([A-Z]{3}-[0-9]{3})",          # ABC-123 格式
        r"([0-9]{3}-[A-Z]{3})",          # 123-ABC 格式
        r"([A-Z0-9]{6})",                # 6位无分隔符
        r"验证码[：:]\s*([A-Z0-9-]+)",    # 中文标识
        r"code[：:]\s*([A-Z0-9-]+)",     # 英文标识
        r"verification[：:]\s*([A-Z0-9-]+)",  # 英文verification
        # 在HTML表格中的验证码
        r"<td[^>]*>([A-Z0-9]{3}-[A-Z0-9]{3})</td>",  # 表格中的验证码
        r"bold;\">([A-Z0-9]{3}-[A-Z0-9]{3})</td>",   # 粗体表格中的验证码
    ]

    for i, pattern in enumerate(patterns):
        match = re.search(pattern, email_content, re.IGNORECASE)
        if match:
            verify_code = match.group(1).replace("-", "")
            print(f"使用模式{i+1}提取验证码: {verify_code}")
            return verify_code

    return None