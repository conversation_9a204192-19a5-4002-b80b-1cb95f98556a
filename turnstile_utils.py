import time, requests

def create_turnstile_task(siteurl, sitekey):
    url = f"http://127.0.0.1:5000/turnstile?url={siteurl}&sitekey={sitekey}"

    response = requests.get(url, timeout=5)
    response.raise_for_status()
    result = response.json()
    task_id = result['task_id']
    return task_id

def get_turnstile_response(task_id):
    url = f"http://127.0.0.1:5000/result?id={task_id}"
    for attempt in range(30):  # 增加尝试次数
        try:
            response = requests.get(url, timeout=3)  # 增加超时时间
            response.raise_for_status()
            result = response.json()

            captcha = result.get('value', None)
            if captcha:
                if captcha != "CAPTCHA_FAIL":
                    print(f"✅ 验证码获取成功 (第{attempt+1}次尝试)")
                    return captcha
                else:
                    print(f"❌ 验证码失败，继续尝试 (第{attempt+1}次)")
                    continue
            else:
                print(f"🔄 等待验证码结果 (第{attempt+1}次)")
                time.sleep(1)  # 增加等待时间
        except Exception as e:
            print(f"⚠️ 请求异常 (第{attempt+1}次): {e}")
            time.sleep(1)

    print("❌ 验证码获取超时")
    return None

def solve_turnstile_captcha():
    """解决Turnstile验证码 - 完整流程"""
    try:
        siteurl = "https://targon.com/sign-in?mode=signup"
        sitekey = "0x4AAAAAABneb-oLvPPTgj0A"

        # 创建任务
        task_id = create_turnstile_task(siteurl, sitekey)
        if not task_id:
            return None

        # 获取结果
        captcha_token = get_turnstile_response(task_id)
        return captcha_token

    except Exception as e:
        print(f"❌ 验证码解决失败: {e}")
        return None