import time, requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

def create_robust_session():
    """创建具有重试机制的会话"""
    session = requests.Session()

    # 配置重试策略
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"]
    )

    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    return session

def create_turnstile_task(siteurl, sitekey):
    """创建Turnstile验证码任务"""
    session = create_robust_session()
    url = f"http://127.0.0.1:5000/turnstile?url={siteurl}&sitekey={sitekey}"

    for attempt in range(3):
        try:
            response = session.get(url, timeout=10)
            response.raise_for_status()
            result = response.json()
            task_id = result.get('task_id')
            if task_id:
                return task_id
            else:
                print(f"❌ 未获取到task_id: {result}")
                continue
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 创建验证码任务连接错误 (第{attempt+1}次): {e}")
            if attempt < 2:
                time.sleep(2)
                continue
        except Exception as e:
            print(f"❌ 创建验证码任务异常 (第{attempt+1}次): {e}")
            if attempt < 2:
                time.sleep(2)
                continue

    return None

def get_turnstile_response(task_id):
    """获取Turnstile验证码结果"""
    session = create_robust_session()
    url = f"http://127.0.0.1:5000/result?id={task_id}"

    for attempt in range(35):  # 增加尝试次数
        try:
            response = session.get(url, timeout=5)
            response.raise_for_status()

            try:
                result = response.json()
            except ValueError as e:
                print(f"⚠️ JSON解析失败 (第{attempt+1}次): {e}")
                time.sleep(1)
                continue

            captcha = result.get('value', None)
            if captcha:
                if captcha != "CAPTCHA_FAIL":
                    print(f"✅ 验证码获取成功 (第{attempt+1}次尝试)")
                    return captcha
                else:
                    print(f"❌ 验证码失败，继续尝试 (第{attempt+1}次)")
                    time.sleep(2)
                    continue
            else:
                if attempt % 5 == 0:  # 每5次打印一次状态
                    print(f"🔄 等待验证码结果 (第{attempt+1}次)")
                time.sleep(1.5)

        except requests.exceptions.ConnectionError as e:
            print(f"⚠️ 连接异常 (第{attempt+1}次): {e}")
            time.sleep(2)
        except requests.exceptions.Timeout as e:
            print(f"⚠️ 请求超时 (第{attempt+1}次): {e}")
            time.sleep(1)
        except Exception as e:
            print(f"⚠️ 请求异常 (第{attempt+1}次): {e}")
            time.sleep(1)

    print("❌ 验证码获取超时")
    return None

def solve_turnstile_captcha():
    """解决Turnstile验证码 - 完整流程"""
    try:
        siteurl = "https://targon.com/sign-in?mode=signup"
        sitekey = "0x4AAAAAABneb-oLvPPTgj0A"

        # 创建任务
        task_id = create_turnstile_task(siteurl, sitekey)
        if not task_id:
            return None

        # 获取结果
        captcha_token = get_turnstile_response(task_id)
        return captcha_token

    except Exception as e:
        print(f"❌ 验证码解决失败: {e}")
        return None