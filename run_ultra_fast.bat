@echo off
chcp 65001 >nul
title Targon Ultra Fast Register

echo.
echo === Targon Ultra Fast Register ===
echo Mode: ULTRA FAST - Absolutely Fast!
echo Threads: 200 (Default)
echo Feature: Parallel Email + Captcha
echo Advantage: Maximum Speed
echo ===================================
echo.

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found
    pause
    exit /b 1
)

REM Check files
if not exist "ultra_fast_register.py" (
    echo ERROR: ultra_fast_register.py not found
    pause
    exit /b 1
)

REM Check Turnstile service
echo Checking Turnstile service...
curl -s http://127.0.0.1:5000 >nul 2>&1
if errorlevel 1 (
    echo WARNING: Turnstile service not running
    echo Please start Turnstile-Solver service first
    echo.
    set /p choice="Continue anyway? (y/N): "
    if /i not "%choice%"=="y" (
        echo Cancelled
        pause
        exit /b 1
    )
) else (
    echo Turnstile service is running
)

echo.
echo Starting ULTRA FAST register...
echo Preparing for MAXIMUM SPEED...
echo.

REM Run ultra fast register
python ultra_fast_register.py

echo.
echo Ultra fast registration completed!
echo Result files:
echo    - ultra_fast_accounts.txt
echo.
pause
