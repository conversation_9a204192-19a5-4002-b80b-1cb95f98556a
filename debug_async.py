#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试异步注册机
"""

import time
import threading
import queue
from email_utils import create_test_email, fetch_first_email, extract_verification_token
from turnstile_utils import solve_turnstile_captcha

def test_single_flow():
    """测试单个完整流程"""
    print("🔍 测试单个完整流程")
    print("=" * 40)
    
    try:
        # 1. 测试邮箱创建
        print("1️⃣ 测试邮箱创建...")
        email_result = create_test_email()
        if not email_result:
            print("❌ 邮箱创建失败")
            return False
        
        email_id, email_address = email_result
        print(f"✅ 邮箱创建成功: {email_address}")
        
        # 2. 测试验证码解决
        print("2️⃣ 测试验证码解决...")
        captcha_token = solve_turnstile_captcha()
        if not captcha_token:
            print("❌ 验证码解决失败")
            return False
        
        print(f"✅ 验证码解决成功: {captcha_token[:50]}...")
        
        # 3. 测试注册请求
        print("3️⃣ 测试注册请求...")
        import requests
        import random
        import string
        
        password = ''.join(random.choice(string.ascii_letters + string.digits + "!@#$%^&*") for _ in range(16))
        
        session = requests.Session()
        session.headers.update({
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'content-type': 'application/json',
            'origin': 'https://targon.com',
            'referer': 'https://targon.com/sign-in?mode=signup'
        })
        
        register_data = {
            "0": {
                "json": {
                    "email": email_address,
                    "password": password,
                    "turnstileToken": captcha_token
                }
            }
        }
        
        try:
            response = session.post(
                'https://targon.com/api/trpc/account.createAccount?batch=1',
                json=register_data,
                timeout=10
            )
            
            print(f"📊 注册响应状态: {response.status_code}")
            print(f"📊 注册响应内容: {response.text[:200]}...")
            
            if response.status_code == 200:
                print("✅ 注册请求成功")
                return True
            else:
                print("❌ 注册请求失败")
                return False
                
        except Exception as e:
            print(f"❌ 注册请求异常: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 测试流程异常: {e}")
        return False

def test_async_worker():
    """测试异步工作模式"""
    print("🔍 测试异步工作模式")
    print("=" * 40)
    
    task_queue = queue.Queue()
    result_queue = queue.Queue()
    
    # 添加测试任务
    for i in range(2):
        task_queue.put(i + 1)
    
    def worker(worker_id):
        """简化的工作线程"""
        print(f"🚀 工作线程 {worker_id} 启动")
        
        while True:
            try:
                task_id = task_queue.get_nowait()
                print(f"⚡ 线程 {worker_id} 开始处理任务 #{task_id}")
                
                # 模拟工作
                time.sleep(2)
                
                print(f"✅ 线程 {worker_id} 完成任务 #{task_id}")
                result_queue.put(f"线程{worker_id}-任务{task_id}")
                task_queue.task_done()
                
            except queue.Empty:
                print(f"💤 线程 {worker_id} 没有更多任务，退出")
                break
            except Exception as e:
                print(f"❌ 线程 {worker_id} 异常: {e}")
                try:
                    task_queue.task_done()
                except:
                    pass
    
    # 启动工作线程
    workers = []
    for i in range(3):
        worker_thread = threading.Thread(target=worker, args=(i + 1,), daemon=True)
        worker_thread.start()
        workers.append(worker_thread)
    
    # 等待完成
    task_queue.join()
    
    # 收集结果
    results = []
    while not result_queue.empty():
        results.append(result_queue.get())
    
    print(f"📊 异步测试结果: {results}")
    return len(results) == 2

def main():
    """主函数"""
    print("🎯 异步注册机调试工具")
    print("=" * 50)
    
    print("选择测试:")
    print("1. 测试单个完整流程")
    print("2. 测试异步工作模式")
    print("3. 退出")
    
    try:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            success = test_single_flow()
            print(f"\n📊 单流程测试结果: {'✅ 成功' if success else '❌ 失败'}")
        elif choice == "2":
            success = test_async_worker()
            print(f"\n📊 异步测试结果: {'✅ 成功' if success else '❌ 失败'}")
        elif choice == "3":
            print("👋 再见!")
            return
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    except Exception as e:
        print(f"❌ 异常: {e}")

if __name__ == "__main__":
    main()
