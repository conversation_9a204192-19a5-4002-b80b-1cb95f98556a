#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Targon 异步注册机 - 真正的能者多劳模式
每个线程独立工作，完成一个任务立即开始下一个
"""

import time
import threading
import queue
import sys
import random
import string
import hmac
import hashlib
import struct
import base64
from email_utils import create_test_email, fetch_first_email, extract_verification_token
from turnstile_utils import solve_turnstile_captcha
import requests
import re

def generate_totp(secret):
    """简单的TOTP生成器"""
    try:
        # 解码base32密钥
        key = base64.b32decode(secret.upper() + '=' * (8 - len(secret) % 8))

        # 获取当前时间戳
        timestamp = int(time.time()) // 30

        # 转换为8字节
        time_bytes = struct.pack('>Q', timestamp)

        # HMAC-SHA1
        hmac_digest = hmac.new(key, time_bytes, hashlib.sha1).digest()

        # 动态截取
        offset = hmac_digest[-1] & 0x0f
        code = struct.unpack('>I', hmac_digest[offset:offset+4])[0]
        code &= 0x7fffffff
        code %= 1000000

        return f"{code:06d}"
    except:
        # 如果失败，返回一个随机6位数
        return f"{random.randint(100000, 999999):06d}"

class AsyncTargonRegister:
    def __init__(self, max_workers=200):
        """异步注册机 - 能者多劳模式"""
        self.max_workers = max_workers
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.worker_status = {}
        self.success_count = 0
        self.failed_count = 0
        self.total_tasks = 0
        self.start_time = None
        self.lock = threading.Lock()
        
        # HTTP会话池
        self.session_pool = queue.Queue()
        self._init_session_pool()
        
        # 输出文件
        self.accounts_file = "targon_accounts.txt"
        self.apikeys_file = "targon_apikeys.txt"
        
        print(f"🚀 异步注册机初始化完成")
        print(f"⚡ 工作线程: {max_workers}")
        print(f"🎯 模式: 能者多劳 (异步并发)")
        
    def _init_session_pool(self):
        """初始化HTTP会话池"""
        headers = {
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'accept': '*/*',
            'Connection': 'keep-alive',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'content-type': 'application/json',
            'origin': 'https://targon.com',
            'priority': 'u=1, i',
            'referer': 'https://targon.com/sign-in?mode=signup',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'x-trpc-source': 'react'
        }
        
        pool_size = min(self.max_workers * 2, 400)
        for _ in range(pool_size):
            session = requests.Session()
            session.headers.update(headers)
            
            # 优化连接池
            adapter = requests.adapters.HTTPAdapter(
                pool_connections=20,
                pool_maxsize=50,
                max_retries=1
            )
            session.mount('http://', adapter)
            session.mount('https://', adapter)
            
            self.session_pool.put(session)
    
    def get_session(self):
        """获取会话"""
        try:
            return self.session_pool.get_nowait()
        except queue.Empty:
            # 动态创建新会话
            session = requests.Session()
            session.headers.update({
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'content-type': 'application/json',
                'origin': 'https://targon.com',
                'referer': 'https://targon.com/sign-in?mode=signup'
            })
            return session
    
    def return_session(self, session):
        """归还会话"""
        try:
            self.session_pool.put_nowait(session)
        except queue.Full:
            pass  # 池满了就丢弃
    
    def generate_password(self):
        """生成随机密码"""
        chars = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(random.choice(chars) for _ in range(16))
    
    def worker(self, worker_id):
        """工作线程 - 能者多劳模式"""
        self.worker_status[worker_id] = "启动中"
        
        while True:
            try:
                # 非阻塞获取任务
                task_id = self.task_queue.get_nowait()
                self.worker_status[worker_id] = f"处理任务 #{task_id}"
                
                # 处理完整的注册流程
                success = self.process_single_account(worker_id, task_id)
                
                with self.lock:
                    if success:
                        self.success_count += 1
                    else:
                        self.failed_count += 1
                
                self.task_queue.task_done()
                
            except queue.Empty:
                # 没有更多任务，线程结束
                self.worker_status[worker_id] = "空闲"
                break
            except Exception as e:
                self.worker_status[worker_id] = f"异常: {str(e)[:20]}"
                with self.lock:
                    self.failed_count += 1
                try:
                    self.task_queue.task_done()
                except:
                    pass
    
    def process_single_account(self, worker_id, task_id):
        """处理单个账户的完整注册流程"""
        try:
            self.worker_status[worker_id] = f"#{task_id} 创建邮箱"
            
            # 1. 创建邮箱 - 立即进行
            email_result = create_test_email()
            if not email_result:
                return False
            
            email_id, email_address = email_result
            password = self.generate_password()
            
            self.worker_status[worker_id] = f"#{task_id} 解决验证码"
            
            # 2. 解决验证码 - 立即进行
            captcha_token = solve_turnstile_captcha()
            if not captcha_token:
                return False
            
            self.worker_status[worker_id] = f"#{task_id} 注册账户"
            
            # 3. 注册账户 - 立即进行
            session = self.get_session()
            try:
                if not self.register_account(session, email_address, password, captcha_token):
                    return False
                
                self.worker_status[worker_id] = f"#{task_id} 验证邮箱"
                
                # 4. 验证邮箱 - 立即进行
                token = self.verify_email_fast(email_id, email_address)
                if not token:
                    return False
                
                self.worker_status[worker_id] = f"#{task_id} 激活账户"
                
                # 5. 激活账户 - 立即进行
                auth_session = self.activate_account(session, token)
                if not auth_session:
                    return False
                
                self.worker_status[worker_id] = f"#{task_id} 设置2FA"
                
                # 6. 设置2FA - 立即进行
                secret = self.setup_2fa(session, auth_session)
                if not secret:
                    return False
                
                self.worker_status[worker_id] = f"#{task_id} 获取API"
                
                # 7. 获取API密钥 - 立即进行
                api_key = self.get_api_key(session, auth_session)
                if not api_key:
                    return False
                
                # 8. 保存结果 - 立即进行
                self.save_account_info(email_address, password, secret, api_key)
                
                self.worker_status[worker_id] = f"#{task_id} ✅完成"
                return True
                
            finally:
                self.return_session(session)
                
        except Exception as e:
            self.worker_status[worker_id] = f"#{task_id} ❌失败: {str(e)[:20]}"
            return False

    def verify_email_fast(self, email_id, email_address):
        """快速验证邮箱 - 紧凑处理"""
        max_attempts = 10
        for attempt in range(max_attempts):
            try:
                email_content = fetch_first_email(email_id)
                if email_content:
                    token = extract_verification_token(email_content)
                    if token:
                        return token

                # 动态等待：前几次快速检查
                wait_time = 1 if attempt < 3 else 2
                time.sleep(wait_time)

            except Exception:
                time.sleep(1)

        return None

    def register_account(self, session, email, password, captcha_token):
        """注册账户"""
        try:
            register_data = {
                "0": {
                    "json": {
                        "email": email,
                        "password": password,
                        "turnstileToken": captcha_token
                    }
                }
            }

            response = session.post(
                'https://targon.com/api/trpc/account.createAccount?batch=1',
                json=register_data,
                timeout=8
            )

            return response.status_code == 200

        except Exception:
            return False

    def activate_account(self, session, token):
        """激活账户"""
        try:
            verify_url = f"https://targon.com/email-verification/?token={token}"
            response = session.get(verify_url, timeout=8, allow_redirects=True)

            if response.status_code == 200:
                # 检查auth_session cookie
                for cookie in session.cookies:
                    if cookie.name == 'auth_session':
                        return cookie.value

            return None

        except Exception:
            return None

    def setup_2fa(self, session, auth_session):
        """设置2FA"""
        try:
            # 获取2FA URI
            input_param = '{"0":{"json":null},"1":{"json":null},"2":{"json":null}}'
            url = f'https://targon.com/api/trpc/model.getAll,account.createTwoFactorURI,account.getUser?batch=1&input={input_param}'

            response = session.get(url, timeout=8)

            if response.status_code == 200:
                result = response.json()
                if len(result) > 1 and 'result' in result[1]:
                    uri_data = result[1]['result']['data']['json']
                    if uri_data and 'uri' in uri_data:
                        uri = uri_data['uri']

                        # 提取secret
                        secret_match = re.search(r'secret=([A-Z2-7]+)', uri)
                        if secret_match:
                            secret = secret_match.group(1)

                            # 生成TOTP码并启用2FA
                            totp_code = generate_totp(secret)

                            enable_data = {
                                "0": {
                                    "json": {
                                        "code": totp_code
                                    }
                                }
                            }

                            enable_response = session.post(
                                'https://targon.com/api/trpc/account.enable2FA?batch=1',
                                json=enable_data,
                                timeout=8
                            )

                            if enable_response.status_code == 200:
                                return secret

            return None

        except Exception:
            return None

    def get_api_key(self, session, auth_session):
        """获取API密钥"""
        try:
            api_key_data = {
                "0": {
                    "json": {
                        "name": f"API-{int(time.time())}"
                    }
                }
            }

            response = session.post(
                'https://targon.com/api/trpc/keys.createApiKey?batch=1',
                json=api_key_data,
                timeout=8
            )

            if response.status_code == 200:
                result = response.json()
                if result and len(result) > 0 and 'result' in result[0]:
                    api_data = result[0]['result']['data']['json']
                    if api_data and 'key' in api_data:
                        return api_data['key']

            return None

        except Exception:
            return None

    def save_account_info(self, email, password, secret, api_key):
        """保存账户信息"""
        try:
            # 保存完整账户信息
            account_info = f"{email}:{password}:{secret}\n"
            with open(self.accounts_file, 'a', encoding='utf-8') as f:
                f.write(account_info)

            # 保存API密钥
            api_info = f"{api_key}\n"
            with open(self.apikeys_file, 'a', encoding='utf-8') as f:
                f.write(api_info)

        except Exception:
            pass

    def show_progress(self):
        """显示实时进度"""
        while True:
            time.sleep(1)  # 每秒更新

            with self.lock:
                total_processed = self.success_count + self.failed_count
                remaining = self.total_tasks - total_processed

                if self.total_tasks > 0:
                    progress = total_processed / self.total_tasks
                    progress_percent = progress * 100

                    # 进度条
                    bar_length = 40
                    filled_length = int(bar_length * progress)
                    bar = '█' * filled_length + '░' * (bar_length - filled_length)

                    # 计算速度
                    elapsed = time.time() - self.start_time if self.start_time else 0
                    speed = self.success_count / elapsed if elapsed > 0 else 0

                    # 工作线程状态
                    active_workers = sum(1 for status in self.worker_status.values() if "处理任务" in status)

                    # 清除当前行并显示进度
                    sys.stdout.write('\r')
                    sys.stdout.write(f'📊 [{bar}] {progress_percent:.1f}% | ✅{self.success_count} ❌{self.failed_count} | 剩余:{remaining} | 速度:{speed:.1f}/秒 | 活跃:{active_workers}线程')
                    sys.stdout.flush()

                    # 完成检查
                    if total_processed >= self.total_tasks:
                        print()  # 换行
                        break

    def run(self, total_count):
        """运行异步注册 - 能者多劳模式"""
        self.total_tasks = total_count
        self.start_time = time.time()

        print(f"\n🚀 开始异步注册 {total_count} 个账户")
        print(f"⚡ 工作线程: {self.max_workers}")
        print(f"🎯 模式: 能者多劳 (真正异步)")
        print("=" * 80)

        # 填充任务队列
        for i in range(total_count):
            self.task_queue.put(i + 1)

        # 启动工作线程
        workers = []
        for i in range(self.max_workers):
            worker = threading.Thread(target=self.worker, args=(i + 1,), daemon=True)
            worker.start()
            workers.append(worker)

        # 启动进度监控
        progress_thread = threading.Thread(target=self.show_progress, daemon=True)
        progress_thread.start()

        # 等待所有任务完成
        self.task_queue.join()

        # 等待所有线程结束
        for worker in workers:
            worker.join(timeout=1)

        # 最终统计
        elapsed = time.time() - self.start_time
        speed = self.success_count / elapsed if elapsed > 0 else 0

        print(f"\n🎯 注册完成!")
        print(f"📊 总数: {total_count}")
        print(f"✅ 成功: {self.success_count}")
        print(f"❌ 失败: {self.failed_count}")
        print(f"⏱️ 耗时: {elapsed:.1f}秒")
        print(f"🚀 速度: {speed:.2f}个/秒")
        print(f"📈 成功率: {self.success_count/total_count*100:.1f}%")

def main():
    """主函数"""
    print("🎯 Targon 异步注册机 - 能者多劳模式")
    print("=" * 60)

    try:
        total_count = int(input("请输入要注册的账户数量: "))

        print("\n⚡ 线程数建议:")
        print("   🔥 极速: 300+ (高性能机器)")
        print("   ⚡ 高速: 200 (推荐)")
        print("   🚀 标准: 100 (稳定)")
        print("   💡 保守: 50 (安全)")

        max_workers_input = input(f"请输入工作线程数 (默认200): ").strip()
        max_workers = int(max_workers_input) if max_workers_input else 200

        if total_count <= 0 or max_workers <= 0:
            print("❌ 数量和线程数必须大于0")
            return

        print(f"\n🚀 配置确认:")
        print(f"   📊 注册数量: {total_count}")
        print(f"   ⚡ 工作线程: {max_workers}")
        print(f"   🎯 模式: 异步并发 (能者多劳)")

        confirm = input("\n确认开始? (y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 已取消")
            return

        # 创建异步注册机
        register = AsyncTargonRegister(max_workers=max_workers)

        # 开始注册
        register.run(total_count)

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 程序异常: {e}")

if __name__ == "__main__":
    main()
