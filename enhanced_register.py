#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版注册机 - 专门解决网络连接问题
"""

import requests
import threading
import time
import random
import string
import json
import urllib.parse
from concurrent.futures import ThreadPoolExecutor, as_completed
from email_utils import create_test_email, fetch_first_email, extract_verification_token
from totp_utils import generate_totp_code
from turnstile_utils import create_turnstile_task, get_turnstile_response
from tqdm import tqdm
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class EnhancedRegister:
    def __init__(self, max_workers=5):  # 默认降低并发数
        self.max_workers = max_workers
        self.success_count = 0
        self.failed_count = 0
        self.lock = threading.Lock()
        self.file_lock = threading.Lock()
        self.progress_bar = None
        self.request_delay = 1.0  # 请求间隔

        print(f"🚀 初始化增强版注册机，并发数: {max_workers}")

        # 请求头
        self.headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'content-type': 'application/json',
            'origin': 'https://targon.com',
            'referer': 'https://targon.com/sign-in?mode=signup',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'x-trpc-source': 'react'
        }

        # 初始化输出文件
        self._init_output_files()

    def _init_output_files(self):
        """初始化输出文件"""
        try:
            import os
            if not os.path.exists('enhanced_accounts.txt'):
                with open('enhanced_accounts.txt', 'w', encoding='utf-8') as f:
                    f.write("")
                print("✅ 创建新的 enhanced_accounts.txt 文件")
            else:
                print("✅ 检测到已有 enhanced_accounts.txt 文件，将追加新数据")

            if not os.path.exists('enhanced_apikeys.txt'):
                with open('enhanced_apikeys.txt', 'w', encoding='utf-8') as f:
                    f.write("")
                print("✅ 创建新的 enhanced_apikeys.txt 文件")
            else:
                print("✅ 检测到已有 enhanced_apikeys.txt 文件，将追加新数据")

        except Exception as e:
            print(f"❌ 初始化输出文件失败: {e}")

    def create_robust_session(self):
        """创建具有强化重试机制的会话"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=8,  # 增加重试次数
            backoff_factor=2,  # 增加退避因子
            status_forcelist=[429, 500, 502, 503, 504, 520, 521, 522, 523, 524],
            allowed_methods=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"],
            raise_on_status=False  # 不在重试失败时抛出异常
        )
        
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=5,  # 减少连接池大小
            pool_maxsize=10,
            socket_options=[(1, 1)]  # SO_REUSEADDR
        )
        
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置默认配置
        session.verify = False  # 禁用SSL验证
        session.timeout = 20  # 增加超时时间
        
        # 添加连接保持头
        session.headers.update({
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        })
        
        return session

    def generate_password(self):
        """生成随机密码"""
        chars = string.ascii_letters + string.digits
        password = "!" + ''.join(random.choices(string.ascii_uppercase, k=1)) + \
                  ''.join(random.choices(chars, k=10)) + \
                  ''.join(random.choices(string.digits, k=4))
        return password

    def solve_captcha_with_retry(self):
        """解决验证码 - 增强重试"""
        for attempt in range(5):  # 增加重试次数
            try:
                if attempt > 0:
                    wait_time = min(2 ** attempt, 15)  # 指数退避
                    print(f"⏳ 验证码重试等待 {wait_time} 秒...")
                    time.sleep(wait_time)
                
                print(f"🔄 开始解决Turnstile验证码 (第{attempt+1}次)...")

                siteurl = "https://targon.com/sign-in?mode=signup"
                sitekey = "0x4AAAAAABneb-oLvPPTgj0A"

                task_id = create_turnstile_task(siteurl, sitekey)
                if not task_id:
                    print(f"❌ 创建验证码任务失败 (第{attempt+1}次)")
                    continue

                print(f"🔄 验证码任务ID: {task_id}")

                # 获取验证码结果
                captcha_token = get_turnstile_response(task_id)

                if captcha_token:
                    print(f"✅ 验证码解决成功: {captcha_token[:20]}...")
                    return captcha_token
                else:
                    print(f"❌ 验证码解决失败 (第{attempt+1}次)")
                    continue

            except Exception as e:
                print(f"❌ 验证码解决异常 (第{attempt+1}次): {e}")
                continue
        
        print("❌ 验证码解决最终失败")
        return None

    def register_account_enhanced(self, email, password):
        """增强版注册账户"""
        session = None
        
        for attempt in range(5):  # 增加重试次数
            try:
                if attempt > 0:
                    wait_time = min(2 ** attempt, 10)
                    print(f"⏳ 注册重试等待 {wait_time} 秒...")
                    time.sleep(wait_time)
                
                session = self.create_robust_session()
                session.headers.update(self.headers)
                
                # 解决验证码
                captcha_token = self.solve_captcha_with_retry()
                if not captcha_token:
                    print(f"❌ 验证码解决失败: {email}")
                    continue

                # 注册请求
                register_data = {
                    "0": {
                        "json": {
                            "email": email,
                            "password": password,
                            "turnstileToken": captcha_token
                        }
                    }
                }

                print(f"🔄 注册中: {email} (第{attempt+1}次)")

                # 添加请求前延迟
                time.sleep(self.request_delay)

                response = session.post(
                    'https://targon.com/api/trpc/account.createAccount?batch=1',
                    json=register_data,
                    timeout=20
                )

                if response.status_code == 200:
                    try:
                        result = response.json()
                        # 检查响应是否包含错误信息
                        if result:
                            print(f"✅ 注册成功: {email}")
                            return session, True
                        else:
                            print(f"❌ 注册响应为空: {email}")
                            continue
                    except ValueError as e:
                        print(f"❌ 注册响应JSON解析失败: {email} - {e}")
                        print(f"   响应内容: {response.text[:200]}")
                        continue
                else:
                    print(f"❌ 注册失败: {email} - {response.status_code}")
                    if response.text:
                        print(f"   响应内容: {response.text[:200]}")
                    continue

            except requests.exceptions.SSLError as e:
                print(f"❌ 注册SSL错误 (第{attempt+1}次): {email} - {e}")
                continue
            except requests.exceptions.ConnectionError as e:
                print(f"❌ 注册连接错误 (第{attempt+1}次): {email} - {e}")
                continue
            except requests.exceptions.Timeout as e:
                print(f"❌ 注册超时 (第{attempt+1}次): {email} - {e}")
                continue
            except Exception as e:
                print(f"❌ 注册异常 (第{attempt+1}次): {email} - {e}")
                continue
        
        print(f"❌ 注册最终失败: {email}")
        return session or self.create_robust_session(), False

    def verify_email_enhanced(self, email_id, email_address):
        """增强版邮箱验证"""
        max_attempts = 20  # 增加尝试次数
        for attempt in range(max_attempts):
            try:
                # 添加请求间隔
                time.sleep(2)
                
                email_content = fetch_first_email(email_id)
                if email_content:
                    token = extract_verification_token(email_content)
                    if token:
                        print(f"✅ 获取验证token: {email_address}")
                        return token

                if attempt < max_attempts - 1:
                    wait_time = 3 if attempt < 10 else 5
                    if attempt % 5 == 0:  # 每5次打印一次状态
                        print(f"🔄 等待验证邮件 (第{attempt+1}次): {email_address}")
                    time.sleep(wait_time)
                    
            except Exception as e:
                print(f"❌ 获取邮件异常: {email_address} - {e}")
                time.sleep(3)
                
        print(f"❌ 未收到验证邮件: {email_address}")
        return None

    def update_progress(self, description=""):
        """更新进度条"""
        if self.progress_bar:
            with self.lock:
                completed = self.success_count + self.failed_count
                self.progress_bar.n = completed
                if description:
                    self.progress_bar.set_description(f"进度 | 成功:{self.success_count} 失败:{self.failed_count} | {description}")
                else:
                    self.progress_bar.set_description(f"进度 | 成功:{self.success_count} 失败:{self.failed_count}")
                self.progress_bar.refresh()

    def activate_account_enhanced(self, session, token):
        """增强版激活账户"""
        for attempt in range(5):
            try:
                if attempt > 0:
                    wait_time = min(2 ** attempt, 10)
                    print(f"⏳ 激活重试等待 {wait_time} 秒...")
                    time.sleep(wait_time)

                verify_url = f"https://targon.com/email-verification/?token={token}"

                # 添加请求前延迟
                time.sleep(self.request_delay)

                response = session.get(verify_url, timeout=20, allow_redirects=True)

                if response.status_code == 200:
                    auth_session = None
                    for cookie in session.cookies:
                        if cookie.name == 'auth_session':
                            auth_session = cookie.value
                            break

                    if auth_session:
                        print(f"✅ 账户激活成功")
                        return auth_session
                    else:
                        print(f"❌ 激活成功但未获取到session (第{attempt+1}次)")
                        continue
                else:
                    print(f"❌ 激活失败: {response.status_code} (第{attempt+1}次)")
                    continue

            except requests.exceptions.SSLError as e:
                print(f"❌ 激活SSL错误 (第{attempt+1}次): {e}")
                continue
            except requests.exceptions.ConnectionError as e:
                print(f"❌ 激活连接错误 (第{attempt+1}次): {e}")
                continue
            except requests.exceptions.Timeout as e:
                print(f"❌ 激活超时 (第{attempt+1}次): {e}")
                continue
            except Exception as e:
                print(f"❌ 激活异常 (第{attempt+1}次): {e}")
                continue

        print("❌ 激活最终失败")
        return None

    def create_two_factor_uri_enhanced(self, session, auth_session):
        """增强版创建2FA URI"""
        for attempt in range(3):
            try:
                if attempt > 0:
                    time.sleep(3)

                session.cookies.update({'auth_session': auth_session})
                session.headers.update({
                    'referer': 'https://targon.com/two-factor-auth',
                    'x-trpc-source': 'react'
                })

                batch_data = {
                    "0": {"json": None, "meta": {"values": ["undefined"]}},
                    "1": {"json": None, "meta": {"values": ["undefined"]}},
                    "2": {"json": None, "meta": {"values": ["undefined"]}}
                }

                input_param = urllib.parse.quote(json.dumps(batch_data))
                url = f'https://targon.com/api/trpc/model.getAll,account.createTwoFactorURI,account.getUser?batch=1&input={input_param}'

                # 添加请求前延迟
                time.sleep(self.request_delay)

                response = session.get(url, timeout=20)

                if response.status_code == 200:
                    try:
                        result = response.json()
                        if result and len(result) >= 2 and 'result' in result[1]:
                            data = result[1]['result']['data']['json']
                            two_factor_secret = data.get('twoFactorSecret')
                            manual_code = data.get('manualCode')
                            if two_factor_secret and manual_code:
                                print(f"✅ 获取2FA secret成功")
                                return two_factor_secret, manual_code

                        # 备用解析方式
                        for item in result:
                            if 'result' in item and 'data' in item['result']:
                                data = item['result']['data']['json']
                                if isinstance(data, dict) and 'twoFactorSecret' in data:
                                    two_factor_secret = data.get('twoFactorSecret')
                                    manual_code = data.get('manualCode')
                                    if two_factor_secret and manual_code:
                                        print(f"✅ 获取2FA secret成功")
                                        return two_factor_secret, manual_code

                        print(f"❌ 2FA数据格式错误 (第{attempt+1}次)")
                        continue

                    except ValueError as e:
                        print(f"❌ 2FA响应JSON解析失败 (第{attempt+1}次): {e}")
                        continue
                else:
                    print(f"❌ 获取2FA URI失败: {response.status_code} (第{attempt+1}次)")
                    continue

            except Exception as e:
                print(f"❌ 获取2FA URI异常 (第{attempt+1}次): {e}")
                continue

        print("❌ 获取2FA URI最终失败")
        return None, None

    def enable_two_factor_enhanced(self, session, auth_session, two_factor_secret, manual_code):
        """增强版启用2FA"""
        for attempt in range(3):
            try:
                if attempt > 0:
                    time.sleep(3)

                session.cookies.update({'auth_session': auth_session})
                otp_code = generate_totp_code(manual_code)
                print(f"✅ 生成TOTP验证码: {otp_code}")

                enable_data = {
                    "0": {
                        "json": {
                            "otp": otp_code,
                            "twoFactorSecret": two_factor_secret
                        }
                    }
                }

                # 添加请求前延迟
                time.sleep(self.request_delay)

                response = session.post(
                    'https://targon.com/api/trpc/account.enable2FA?batch=1',
                    json=enable_data,
                    timeout=20
                )

                if response.status_code == 200:
                    print(f"✅ 2FA启用成功")
                    return True
                else:
                    print(f"❌ 启用2FA失败: {response.status_code} (第{attempt+1}次)")
                    continue

            except Exception as e:
                print(f"❌ 启用2FA异常 (第{attempt+1}次): {e}")
                continue

        print("❌ 启用2FA最终失败")
        return False

    def get_api_key_enhanced(self, session, auth_session):
        """增强版获取API密钥"""
        for attempt in range(3):
            try:
                if attempt > 0:
                    time.sleep(3)

                session.cookies.update({'auth_session': auth_session})

                api_key_data = {
                    "0": {
                        "json": {
                            "name": f"Key_{random.randint(1000, 9999)}"
                        }
                    }
                }

                # 添加请求前延迟
                time.sleep(self.request_delay)

                response = session.post(
                    'https://targon.com/api/trpc/keys.createApiKey?batch=1',
                    json=api_key_data,
                    timeout=20
                )

                if response.status_code == 200:
                    try:
                        result = response.json()
                        if result and len(result) > 0 and 'result' in result[0]:
                            api_key = result[0]['result']['data']['json']
                            print(f"✅ 获取API密钥成功")
                            return api_key
                        else:
                            print(f"❌ API密钥响应格式错误 (第{attempt+1}次)")
                            continue
                    except ValueError as e:
                        print(f"❌ API密钥响应JSON解析失败 (第{attempt+1}次): {e}")
                        continue
                else:
                    print(f"❌ 获取API密钥失败: {response.status_code} (第{attempt+1}次)")
                    continue

            except Exception as e:
                print(f"❌ 获取API密钥异常 (第{attempt+1}次): {e}")
                continue

        print("❌ 获取API密钥最终失败")
        return None

    def process_single_account_enhanced(self, index):
        """增强版处理单个账户的完整流程"""
        try:
            self.update_progress(f"开始处理账户 #{index}")

            # 1. 创建邮箱
            self.update_progress(f"#{index} 创建邮箱中...")
            email_result = create_test_email()
            if not email_result:
                with self.lock:
                    self.failed_count += 1
                self.update_progress(f"#{index} 创建邮箱失败")
                return

            email_id, email_address = email_result

            # 2. 生成密码
            password = self.generate_password()

            # 3. 注册账户
            self.update_progress(f"#{index} 注册账户中...")
            session, register_success = self.register_account_enhanced(email_address, password)
            if not register_success:
                with self.lock:
                    self.failed_count += 1
                self.update_progress(f"#{index} 注册失败")
                return

            # 4. 验证邮箱
            self.update_progress(f"#{index} 验证邮箱中...")
            token = self.verify_email_enhanced(email_id, email_address)
            if not token:
                with self.lock:
                    self.failed_count += 1
                self.update_progress(f"#{index} 邮箱验证失败")
                return

            # 5. 激活账户
            self.update_progress(f"#{index} 激活账户中...")
            auth_session = self.activate_account_enhanced(session, token)
            if not auth_session:
                with self.lock:
                    self.failed_count += 1
                self.update_progress(f"#{index} 账户激活失败")
                return

            # 6. 设置2FA验证
            self.update_progress(f"#{index} 设置2FA中...")
            two_factor_secret, manual_code = self.create_two_factor_uri_enhanced(session, auth_session)
            if not two_factor_secret or not manual_code:
                with self.lock:
                    self.failed_count += 1
                self.update_progress(f"#{index} 获取2FA信息失败")
                return

            # 7. 启用2FA
            self.update_progress(f"#{index} 启用2FA中...")
            twofa_enabled = self.enable_two_factor_enhanced(session, auth_session, two_factor_secret, manual_code)
            twofa_status = "2FA_SUCCESS" if twofa_enabled else "2FA_FAILED"

            # 8. 获取API密钥
            self.update_progress(f"#{index} 获取API密钥中...")
            api_key = self.get_api_key_enhanced(session, auth_session)
            if not api_key:
                with self.lock:
                    self.failed_count += 1
                self.update_progress(f"#{index} 获取API密钥失败")
                return

            # 9. 保存结果
            cookie_str = f"auth_session={auth_session}"
            result_line = f"{email_address}----{password}----{cookie_str}----{api_key}----{manual_code}----{twofa_status}"

            self._write_to_files(result_line, api_key)

            with self.lock:
                self.success_count += 1

            self.update_progress(f"#{index} 完成: {email_address[:20]}...")

        except Exception as e:
            with self.lock:
                self.failed_count += 1
            self.update_progress(f"#{index} 异常: {str(e)[:30]}...")

    def _write_to_files(self, result_line, api_key):
        """写入文件"""
        with self.file_lock:
            try:
                with open('enhanced_accounts.txt', 'a', encoding='utf-8') as f:
                    f.write(result_line + '\n')
                    f.flush()

                with open('enhanced_apikeys.txt', 'a', encoding='utf-8') as f:
                    f.write(api_key + '\n')
                    f.flush()

            except Exception as e:
                print(f"❌ 写入文件失败: {e}")

    def run(self, total_count):
        """运行增强版注册机"""
        print(f"🚀 开始批量注册 {total_count} 个账户，并发数: {self.max_workers}")
        print("🔧 增强版特性:")
        print("   - 强化网络重试机制")
        print("   - 智能请求间隔控制")
        print("   - SSL错误自动处理")
        print("   - 连接池优化")
        print("=" * 60)

        start_time = time.time()

        # 初始化进度条
        self.progress_bar = tqdm(
            total=total_count,
            desc="进度 | 成功:0 失败:0",
            unit="个",
            ncols=100,
            bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}] {desc}"
        )

        try:
            # 使用线程池执行 - 更保守的并发策略
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = []

                # 分批提交任务，减少并发压力
                batch_size = min(2, self.max_workers)  # 每批最多2个
                for i in range(0, total_count, batch_size):
                    batch_end = min(i + batch_size, total_count)

                    # 提交当前批次的任务
                    for j in range(i, batch_end):
                        future = executor.submit(self.process_single_account_enhanced, j+1)
                        futures.append(future)
                        time.sleep(0.5)  # 每个任务间隔0.5秒

                    # 批次间隔
                    if batch_end < total_count:
                        self.update_progress(f"启动第{i//batch_size + 1}批任务，等待2秒...")
                        time.sleep(2.0)  # 增加批次间隔

                # 等待所有任务完成
                for future in as_completed(futures):
                    try:
                        future.result()
                    except Exception as e:
                        with self.lock:
                            self.failed_count += 1
                        self.update_progress(f"任务执行异常: {str(e)[:30]}...")

        finally:
            # 关闭进度条
            if self.progress_bar:
                self.progress_bar.close()

        end_time = time.time()
        elapsed_time = end_time - start_time

        # 输出统计信息
        print("\n" + "=" * 60)
        print(f"📊 增强版注册完成统计:")
        print(f"   总数量: {total_count}")
        print(f"   成功: {self.success_count}")
        print(f"   失败: {self.failed_count}")
        print(f"   成功率: {self.success_count/total_count*100:.1f}%")
        print(f"   耗时: {elapsed_time:.1f}秒")
        if self.success_count > 0:
            print(f"   平均速度: {self.success_count/elapsed_time:.2f}个/秒")
        print(f"✅ 完整账户信息: enhanced_accounts.txt")
        print(f"✅ API密钥文件: enhanced_apikeys.txt")

def main():
    """主函数"""
    print("🎯 增强版注册机 - 专门解决网络连接问题")
    print("=" * 60)

    try:
        total_count = int(input("请输入要注册的账户数量: "))
        max_workers = int(input(f"请输入并发线程数 (推荐3-5): ") or "3")

        if total_count <= 0 or max_workers <= 0:
            print("❌ 数量和线程数必须大于0")
            return

        if max_workers > 10:
            print("⚠️ 警告: 并发数过高可能导致网络问题，建议不超过10")
            confirm = input("是否继续? (y/N): ").lower()
            if confirm != 'y':
                return

        print(f"\n📋 配置信息:")
        print(f"   注册数量: {total_count}")
        print(f"   并发线程: {max_workers}")
        print(f"   增强特性: 已启用")

        confirm = input("\n确认开始注册? (y/N): ").lower()
        if confirm != 'y':
            print("❌ 已取消注册")
            return

        # 开始注册
        register = EnhancedRegister(max_workers=max_workers)
        register.run(total_count)

    except KeyboardInterrupt:
        print("\n❌ 用户中断注册")
    except ValueError:
        print("❌ 输入格式错误，请输入数字")
    except Exception as e:
        print(f"❌ 程序异常: {e}")

if __name__ == "__main__":
    main()
