@echo off
chcp 65001 >nul
title Targon 注册机 - 集成Turnstile验证码

echo ========================================
echo    Targon 高性能注册机 v2.0
echo    集成Cloudflare Turnstile验证码
echo ========================================
echo.

echo 🔧 检查依赖...
python -c "import requests, threading, time, random, string, re, json, urllib.parse, concurrent.futures, queue, sys" 2>nul
if errorlevel 1 (
    echo ❌ Python依赖缺失，请安装必要的库
    pause
    exit /b 1
)

echo 📧 检查邮箱工具...
python -c "from email_utils import create_test_email, fetch_first_email, extract_verification_token" 2>nul
if errorlevel 1 (
    echo ❌ 邮箱工具缺失，请确保email_utils.py存在
    pause
    exit /b 1
)

echo 🔐 检查TOTP工具...
python -c "from totp_utils import generate_totp_code" 2>nul
if errorlevel 1 (
    echo ❌ TOTP工具缺失，请确保totp_utils.py存在
    pause
    exit /b 1
)

echo 🤖 检查Turnstile工具...
python -c "from turnstile_utils import create_turnstile_task, get_turnstile_response" 2>nul
if errorlevel 1 (
    echo ❌ Turnstile工具缺失，请确保turnstile_utils.py存在
    echo ❌ 请确保Turnstile解决服务正在运行 (http://127.0.0.1:5000)
    pause
    exit /b 1
)

echo ✅ 所有依赖检查完成
echo.

echo 🧪 是否先进行集成测试？(y/n)
set /p test_choice=请选择: 
if /i "%test_choice%"=="y" (
    echo.
    echo 🧪 运行集成测试...
    python test_turnstile_integration.py
    echo.
    echo 测试完成，按任意键继续...
    pause >nul
    echo.
)

echo 请选择运行模式:
echo [1] 快速模式 - 10个账户，50线程
echo [2] 标准模式 - 50个账户，100线程  
echo [3] 高速模式 - 100个账户，200线程
echo [4] 极速模式 - 500个账户，无限线程
echo [5] 自定义模式
echo.

set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" (
    set count=10
    set threads=50
) else if "%choice%"=="2" (
    set count=50
    set threads=100
) else if "%choice%"=="3" (
    set count=100
    set threads=200
) else if "%choice%"=="4" (
    set count=500
    set threads=0
) else if "%choice%"=="5" (
    set /p count=请输入注册数量: 
    set /p threads=请输入线程数 (0=无限): 
) else (
    echo 无效选择，使用默认设置
    set count=10
    set threads=50
)

echo.
echo ⚙️  配置信息:
echo    📊 注册数量: %count%
echo    🧵 线程数: %threads%
echo    🤖 验证码: Turnstile自动解决
echo.

echo 🚀 启动注册机...
python -c "
from targon_register import TargonRegister
import sys

try:
    register = TargonRegister(max_workers=%threads% if %threads% > 0 else None)
    register.run_batch_registration(%count%)
except KeyboardInterrupt:
    print('\n⏹️  用户中断操作')
except Exception as e:
    print(f'\n❌ 运行错误: {e}')
    sys.exit(1)
"

echo.
echo ✅ 注册完成！
echo 📁 结果文件:
echo    - targon_accounts.txt (账户信息)
echo    - targon_api_keys.txt (API密钥)
echo.
pause
