#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Targon 速度测试脚本
测试不同并发数下的注册速度
"""

import time
import sys
from high_speed_register import HighSpeedRegister

def speed_test():
    """速度测试"""
    print("🎯 Targon 速度测试")
    print("=" * 60)
    
    # 测试配置
    test_configs = [
        {"workers": 50, "count": 5, "name": "标准模式"},
        {"workers": 100, "count": 5, "name": "高速模式"},
        {"workers": 200, "count": 5, "name": "极速模式"},
        {"workers": 300, "count": 5, "name": "超极速模式"},
    ]
    
    results = []
    
    for config in test_configs:
        print(f"\n🚀 测试 {config['name']} - 并发数: {config['workers']}")
        print("-" * 40)
        
        try:
            register = HighSpeedRegister(max_workers=config['workers'])
            
            start_time = time.time()
            register.run(config['count'])
            end_time = time.time()
            
            elapsed = end_time - start_time
            speed = config['count'] / elapsed if elapsed > 0 else 0
            success_rate = register.success_count / config['count'] * 100
            
            result = {
                'name': config['name'],
                'workers': config['workers'],
                'count': config['count'],
                'success': register.success_count,
                'elapsed': elapsed,
                'speed': speed,
                'success_rate': success_rate
            }
            
            results.append(result)
            
            print(f"✅ {config['name']} 完成:")
            print(f"   成功: {register.success_count}/{config['count']}")
            print(f"   耗时: {elapsed:.1f}秒")
            print(f"   速度: {speed:.2f}个/秒")
            print(f"   成功率: {success_rate:.1f}%")
            
        except Exception as e:
            print(f"❌ {config['name']} 测试失败: {e}")
    
    # 输出测试结果汇总
    print("\n" + "=" * 60)
    print("📊 速度测试结果汇总")
    print("=" * 60)
    
    print(f"{'模式':<12} {'并发数':<8} {'速度':<12} {'成功率':<8} {'耗时':<8}")
    print("-" * 60)
    
    for result in results:
        print(f"{result['name']:<12} {result['workers']:<8} {result['speed']:.2f}个/秒{'':<4} {result['success_rate']:.1f}%{'':<4} {result['elapsed']:.1f}秒")
    
    # 找出最佳配置
    if results:
        best_speed = max(results, key=lambda x: x['speed'])
        best_success = max(results, key=lambda x: x['success_rate'])
        
        print(f"\n🏆 最佳速度: {best_speed['name']} - {best_speed['speed']:.2f}个/秒")
        print(f"🎯 最佳成功率: {best_success['name']} - {best_success['success_rate']:.1f}%")

def quick_test():
    """快速测试 - 只测试1个账户"""
    print("⚡ 快速速度测试 (1个账户)")
    print("=" * 40)
    
    register = HighSpeedRegister(max_workers=300)
    
    start_time = time.time()
    register.run(1)
    end_time = time.time()
    
    elapsed = end_time - start_time
    
    print(f"\n📊 快速测试结果:")
    print(f"   耗时: {elapsed:.1f}秒")
    print(f"   成功: {register.success_count}/1")
    print(f"   成功率: {register.success_count * 100:.1f}%")

def main():
    """主函数"""
    print("🎯 Targon 速度测试工具")
    print("=" * 60)
    
    print("选择测试模式:")
    print("1. 快速测试 (1个账户)")
    print("2. 完整测试 (多种并发配置)")
    print("3. 退出")
    
    try:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            quick_test()
        elif choice == "2":
            speed_test()
        elif choice == "3":
            print("👋 再见!")
            return
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    main()
