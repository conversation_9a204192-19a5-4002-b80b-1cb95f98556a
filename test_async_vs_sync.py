#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步 vs 同步 性能对比测试
"""

import time
import sys
from async_targon_register import AsyncTargonRegister

def test_async_performance():
    """测试异步性能"""
    print("🚀 异步模式性能测试")
    print("=" * 50)
    
    try:
        count = int(input("请输入测试数量 (建议3-5): ") or "3")
        threads = int(input("请输入线程数 (建议50): ") or "50")
        
        if count <= 0 or threads <= 0:
            print("❌ 数量和线程数必须大于0")
            return
            
        print(f"\n🎯 异步测试配置:")
        print(f"   📊 测试数量: {count}")
        print(f"   ⚡ 工作线程: {threads}")
        print(f"   🚀 模式: 能者多劳 (异步)")
        
        confirm = input("\n开始测试? (y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 已取消")
            return
        
        print("\n" + "="*60)
        print("🚀 开始异步性能测试...")
        print("="*60)
        
        register = AsyncTargonRegister(max_workers=threads)
        
        start_time = time.time()
        register.run(count)
        end_time = time.time()
        
        elapsed = end_time - start_time
        speed = register.success_count / elapsed if elapsed > 0 else 0
        
        print("\n" + "="*60)
        print("📊 异步测试结果:")
        print("="*60)
        print(f"   总数: {count}")
        print(f"   成功: {register.success_count}")
        print(f"   失败: {register.failed_count}")
        print(f"   耗时: {elapsed:.1f}秒")
        print(f"   速度: {speed:.2f}个/秒")
        print(f"   成功率: {register.success_count/count*100:.1f}%")
        print(f"   模式: 🚀 异步并发 (能者多劳)")
        
        # 分析性能
        print(f"\n📈 性能分析:")
        if speed > 1.0:
            print(f"   🔥 速度优秀: {speed:.2f}个/秒")
        elif speed > 0.5:
            print(f"   ⚡ 速度良好: {speed:.2f}个/秒")
        else:
            print(f"   💡 速度一般: {speed:.2f}个/秒")
            
        if register.success_count == count:
            print(f"   ✅ 成功率完美: 100%")
        elif register.success_count/count > 0.8:
            print(f"   🎯 成功率优秀: {register.success_count/count*100:.1f}%")
        else:
            print(f"   ⚠️ 成功率需改进: {register.success_count/count*100:.1f}%")
        
    except ValueError:
        print("❌ 请输入有效数字")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def quick_demo():
    """快速演示"""
    print("⚡ 异步模式快速演示")
    print("=" * 40)
    
    print("🎯 演示配置:")
    print("   📊 数量: 2个账户")
    print("   ⚡ 线程: 20个")
    print("   🚀 模式: 异步并发")
    
    confirm = input("\n开始演示? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 已取消")
        return
    
    print("\n🚀 开始异步演示...")
    
    register = AsyncTargonRegister(max_workers=20)
    
    start_time = time.time()
    register.run(2)
    end_time = time.time()
    
    elapsed = end_time - start_time
    speed = register.success_count / elapsed if elapsed > 0 else 0
    
    print(f"\n📊 演示结果:")
    print(f"   成功: {register.success_count}/2")
    print(f"   耗时: {elapsed:.1f}秒")
    print(f"   速度: {speed:.2f}个/秒")
    print(f"   模式: 🚀 异步并发")

def main():
    """主函数"""
    print("🎯 Targon 异步性能测试工具")
    print("=" * 50)
    
    print("选择测试模式:")
    print("1. 快速演示 (2个账户)")
    print("2. 性能测试 (自定义)")
    print("3. 退出")
    
    try:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            quick_demo()
        elif choice == "2":
            test_async_performance()
        elif choice == "3":
            print("👋 再见!")
            return
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    except Exception as e:
        print(f"❌ 异常: {e}")

if __name__ == "__main__":
    main()
