#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整流程注册机 - 基于原版targon_register.py，加上验证码
"""

import requests
import threading
import time
import random
import string
import json
import urllib.parse
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from email_utils import create_test_email, fetch_first_email, extract_verification_token
from totp_utils import generate_totp_code
from turnstile_utils import create_turnstile_task, get_turnstile_response
from tqdm import tqdm

class CompleteRegister:
    def __init__(self, max_workers=50):
        self.max_workers = max_workers
        self.success_count = 0
        self.failed_count = 0
        self.lock = threading.Lock()
        self.file_lock = threading.Lock()
        self.progress_bar = None

        print(f"🚀 初始化完整流程注册机，并发数: {max_workers}")

        # 请求头
        self.headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'content-type': 'application/json',
            'origin': 'https://targon.com',
            'referer': 'https://targon.com/sign-in?mode=signup',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'x-trpc-source': 'react'
        }

        # 初始化输出文件
        self._init_output_files()

    def _init_output_files(self):
        """初始化输出文件 - 追加模式，不清空已有数据"""
        try:
            # 检查文件是否存在，如果不存在则创建
            import os
            if not os.path.exists('complete_accounts.txt'):
                with open('complete_accounts.txt', 'w', encoding='utf-8') as f:
                    f.write("")
                print("✅ 创建新的 complete_accounts.txt 文件")
            else:
                print("✅ 检测到已有 complete_accounts.txt 文件，将追加新数据")

            if not os.path.exists('complete_apikeys.txt'):
                with open('complete_apikeys.txt', 'w', encoding='utf-8') as f:
                    f.write("")
                print("✅ 创建新的 complete_apikeys.txt 文件")
            else:
                print("✅ 检测到已有 complete_apikeys.txt 文件，将追加新数据")

        except Exception as e:
            print(f"❌ 初始化输出文件失败: {e}")

    def generate_password(self):
        """生成随机密码"""
        chars = string.ascii_letters + string.digits
        password = "!" + ''.join(random.choices(string.ascii_uppercase, k=1)) + \
                  ''.join(random.choices(chars, k=10)) + \
                  ''.join(random.choices(string.digits, k=4))
        return password

    def solve_captcha(self):
        """解决验证码"""
        try:
            print("🔄 开始解决Turnstile验证码...")

            # 使用原来的turnstile_utils
            siteurl = "https://targon.com/sign-in?mode=signup"
            sitekey = "0x4AAAAAABneb-oLvPPTgj0A"

            task_id = create_turnstile_task(siteurl, sitekey)
            if not task_id:
                print("❌ 创建验证码任务失败")
                return None

            print(f"🔄 验证码任务ID: {task_id}")

            # 获取验证码结果
            captcha_token = get_turnstile_response(task_id)

            if captcha_token:
                print(f"✅ 验证码解决成功: {captcha_token[:20]}...")
                return captcha_token
            else:
                print("❌ 验证码解决失败")
                return None

        except Exception as e:
            print(f"❌ 验证码解决异常: {e}")
            return None

    def register_account(self, email, password):
        """注册账户"""
        try:
            session = requests.Session()
            session.headers.update(self.headers)
            
            # 解决验证码
            captcha_token = self.solve_captcha()
            if not captcha_token:
                print(f"❌ 验证码解决失败: {email}")
                return session, False

            # 注册请求
            register_data = {
                "0": {
                    "json": {
                        "email": email,
                        "password": password,
                        "turnstileToken": captcha_token
                    }
                }
            }

            print(f"🔄 注册中: {email}")

            response = session.post(
                'https://targon.com/api/trpc/account.createAccount?batch=1',
                json=register_data,
                timeout=10
            )

            if response.status_code == 200:
                print(f"✅ 注册成功: {email}")
                return session, True
            else:
                print(f"❌ 注册失败: {email} - {response.status_code}")
                return session, False

        except Exception as e:
            print(f"❌ 注册异常: {email} - {e}")
            return requests.Session(), False

    def verify_email(self, email_id, email_address):
        """验证邮箱"""
        max_attempts = 15
        for attempt in range(max_attempts):
            try:
                email_content = fetch_first_email(email_id)
                if email_content:
                    token = extract_verification_token(email_content)
                    if token:
                        print(f"✅ 获取验证token: {email_address}")
                        return token

                if attempt < max_attempts - 1:
                    wait_time = 2 if attempt < 5 else 3
                    time.sleep(wait_time)
                    
            except Exception as e:
                print(f"❌ 获取邮件异常: {email_address} - {e}")
                
        print(f"❌ 未收到验证邮件: {email_address}")
        return None

    def activate_account(self, session, token):
        """激活账户"""
        try:
            verify_url = f"https://targon.com/email-verification/?token={token}"
            response = session.get(verify_url, timeout=10, allow_redirects=True)
            
            if response.status_code == 200:
                auth_session = None
                for cookie in session.cookies:
                    if cookie.name == 'auth_session':
                        auth_session = cookie.value
                        break
                
                if auth_session:
                    print(f"✅ 账户激活成功")
                    return auth_session
                else:
                    print(f"❌ 激活成功但未获取到session")
                    return None
            else:
                print(f"❌ 激活失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 激活异常: {e}")
            return None

    def create_two_factor_uri(self, session, auth_session):
        """创建2FA URI"""
        try:
            session.cookies.update({'auth_session': auth_session})
            session.headers.update({
                'referer': 'https://targon.com/two-factor-auth',
                'x-trpc-source': 'react'
            })

            batch_data = {
                "0": {"json": None, "meta": {"values": ["undefined"]}},
                "1": {"json": None, "meta": {"values": ["undefined"]}},
                "2": {"json": None, "meta": {"values": ["undefined"]}}
            }

            input_param = urllib.parse.quote(json.dumps(batch_data))
            url = f'https://targon.com/api/trpc/model.getAll,account.createTwoFactorURI,account.getUser?batch=1&input={input_param}'

            response = session.get(url, timeout=10)

            if response.status_code == 200:
                result = response.json()
                if result and len(result) >= 2 and 'result' in result[1]:
                    data = result[1]['result']['data']['json']
                    two_factor_secret = data.get('twoFactorSecret')
                    manual_code = data.get('manualCode')
                    print(f"✅ 获取2FA secret成功")
                    return two_factor_secret, manual_code
                else:
                    for i, item in enumerate(result):
                        if 'result' in item and 'data' in item['result']:
                            data = item['result']['data']['json']
                            if isinstance(data, dict) and 'twoFactorSecret' in data:
                                two_factor_secret = data.get('twoFactorSecret')
                                manual_code = data.get('manualCode')
                                print(f"✅ 获取2FA secret成功")
                                return two_factor_secret, manual_code
                    return None, None
            else:
                print(f"❌ 获取2FA URI失败: {response.status_code}")
                return None, None

        except Exception as e:
            print(f"❌ 获取2FA URI异常: {e}")
            return None, None

    def enable_two_factor(self, session, auth_session, two_factor_secret, manual_code):
        """启用2FA"""
        try:
            session.cookies.update({'auth_session': auth_session})
            otp_code = generate_totp_code(manual_code)
            print(f"✅ 生成TOTP验证码: {otp_code}")

            enable_data = {
                "0": {
                    "json": {
                        "otp": otp_code,
                        "twoFactorSecret": two_factor_secret
                    }
                }
            }

            response = session.post(
                'https://targon.com/api/trpc/account.enable2FA?batch=1',
                json=enable_data,
                timeout=10
            )

            if response.status_code == 200:
                print(f"✅ 2FA启用成功")
                return True
            else:
                print(f"❌ 启用2FA失败: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ 启用2FA异常: {e}")
            return False

    def get_api_key(self, session, auth_session):
        """获取API密钥"""
        try:
            session.cookies.update({'auth_session': auth_session})

            api_key_data = {
                "0": {
                    "json": {
                        "name": f"Key_{random.randint(1000, 9999)}"
                    }
                }
            }

            response = session.post(
                'https://targon.com/api/trpc/keys.createApiKey?batch=1',
                json=api_key_data,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result and len(result) > 0 and 'result' in result[0]:
                    api_key = result[0]['result']['data']['json']
                    print(f"✅ 获取API密钥成功")
                    return api_key
                else:
                    print(f"❌ API密钥响应格式错误")
                    return None
            else:
                print(f"❌ 获取API密钥失败: {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ 获取API密钥异常: {e}")
            return None

    def update_progress(self, description=""):
        """更新进度条"""
        if self.progress_bar:
            with self.lock:
                completed = self.success_count + self.failed_count
                self.progress_bar.n = completed
                if description:
                    self.progress_bar.set_description(f"进度 | 成功:{self.success_count} 失败:{self.failed_count} | {description}")
                else:
                    self.progress_bar.set_description(f"进度 | 成功:{self.success_count} 失败:{self.failed_count}")
                self.progress_bar.refresh()

    def process_single_account(self, index):
        """处理单个账户的完整流程"""
        try:
            self.update_progress(f"开始处理账户 #{index}")

            # 1. 创建邮箱
            self.update_progress(f"#{index} 创建邮箱中...")
            email_result = create_test_email()
            if not email_result:
                with self.lock:
                    self.failed_count += 1
                self.update_progress(f"#{index} 创建邮箱失败")
                return

            email_id, email_address = email_result

            # 2. 生成密码
            password = self.generate_password()

            # 3. 注册账户
            self.update_progress(f"#{index} 注册账户中...")
            session, register_success = self.register_account(email_address, password)
            if not register_success:
                with self.lock:
                    self.failed_count += 1
                self.update_progress(f"#{index} 注册失败")
                return

            # 4. 验证邮箱
            self.update_progress(f"#{index} 验证邮箱中...")
            token = self.verify_email(email_id, email_address)
            if not token:
                with self.lock:
                    self.failed_count += 1
                self.update_progress(f"#{index} 邮箱验证失败")
                return

            # 5. 激活账户
            self.update_progress(f"#{index} 激活账户中...")
            auth_session = self.activate_account(session, token)
            if not auth_session:
                with self.lock:
                    self.failed_count += 1
                self.update_progress(f"#{index} 账户激活失败")
                return

            # 6. 设置2FA验证
            self.update_progress(f"#{index} 设置2FA中...")
            two_factor_secret, manual_code = self.create_two_factor_uri(session, auth_session)
            if not two_factor_secret or not manual_code:
                with self.lock:
                    self.failed_count += 1
                self.update_progress(f"#{index} 获取2FA信息失败")
                return

            # 7. 启用2FA
            self.update_progress(f"#{index} 启用2FA中...")
            twofa_enabled = self.enable_two_factor(session, auth_session, two_factor_secret, manual_code)
            twofa_status = "2FA_SUCCESS" if twofa_enabled else "2FA_FAILED"

            # 8. 获取API密钥
            self.update_progress(f"#{index} 获取API密钥中...")
            api_key = self.get_api_key(session, auth_session)
            if not api_key:
                with self.lock:
                    self.failed_count += 1
                self.update_progress(f"#{index} 获取API密钥失败")
                return

            # 9. 保存结果
            cookie_str = f"auth_session={auth_session}"
            result_line = f"{email_address}----{password}----{cookie_str}----{api_key}----{manual_code}----{twofa_status}"

            self._write_to_files(result_line, api_key)

            with self.lock:
                self.success_count += 1

            self.update_progress(f"#{index} 完成: {email_address[:20]}...")

        except Exception as e:
            with self.lock:
                self.failed_count += 1
            self.update_progress(f"#{index} 异常: {str(e)[:30]}...")

    def _write_to_files(self, result_line, api_key):
        """写入文件"""
        with self.file_lock:
            try:
                with open('complete_accounts.txt', 'a', encoding='utf-8') as f:
                    f.write(result_line + '\n')
                    f.flush()

                with open('complete_apikeys.txt', 'a', encoding='utf-8') as f:
                    f.write(api_key + '\n')
                    f.flush()

            except Exception as e:
                print(f"❌ 写入文件失败: {e}")

    def run(self, total_count):
        """运行注册机"""
        print(f"🚀 开始批量注册 {total_count} 个账户，并发数: {self.max_workers}")
        print("=" * 60)

        start_time = time.time()

        # 初始化进度条
        self.progress_bar = tqdm(
            total=total_count,
            desc="进度 | 成功:0 失败:0",
            unit="个",
            ncols=100,
            bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}] {desc}"
        )

        try:
            # 使用线程池执行 - 分批启动避免一次性全部启动
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = []

                # 分批提交任务，每批间隔0.5秒
                batch_size = min(5, self.max_workers)  # 每批最多5个
                for i in range(0, total_count, batch_size):
                    batch_end = min(i + batch_size, total_count)

                    # 提交当前批次的任务
                    for j in range(i, batch_end):
                        future = executor.submit(self.process_single_account, j+1)
                        futures.append(future)
                        time.sleep(0.1)  # 每个任务间隔0.1秒

                    # 批次间隔
                    if batch_end < total_count:
                        self.update_progress(f"启动第{i//batch_size + 1}批任务，等待0.5秒...")
                        time.sleep(0.5)

                # 等待所有任务完成
                for future in as_completed(futures):
                    try:
                        future.result()
                    except Exception as e:
                        with self.lock:
                            self.failed_count += 1
                        self.update_progress(f"任务执行异常: {str(e)[:30]}...")

        finally:
            # 关闭进度条
            if self.progress_bar:
                self.progress_bar.close()

        end_time = time.time()
        elapsed_time = end_time - start_time

        # 输出统计信息
        print("\n" + "=" * 60)
        print(f"📊 注册完成统计:")
        print(f"   总数量: {total_count}")
        print(f"   成功: {self.success_count}")
        print(f"   失败: {self.failed_count}")
        print(f"   成功率: {self.success_count/total_count*100:.1f}%")
        print(f"   耗时: {elapsed_time:.1f}秒")
        if self.success_count > 0:
            print(f"   平均速度: {self.success_count/elapsed_time:.2f}个/秒")
        print(f"✅ 完整账户信息: complete_accounts.txt")
        print(f"✅ API密钥文件: complete_apikeys.txt")

def main():
    """主函数"""
    print("🎯 完整流程注册机")
    print("=" * 60)

    try:
        total_count = int(input("请输入要注册的账户数量: "))
        max_workers = int(input(f"请输入并发线程数 (默认20): ") or "20")

        if total_count <= 0 or max_workers <= 0:
            print("❌ 数量和线程数必须大于0")
            return

        print(f"\n📋 配置信息:")
        print(f"   注册数量: {total_count}")
        print(f"   并发线程: {max_workers}")

        confirm = input("\n确认开始注册? (y/N): ").lower()
        if confirm != 'y':
            print("❌ 已取消注册")
            return

        # 开始注册
        register = CompleteRegister(max_workers=max_workers)
        register.run(total_count)

    except KeyboardInterrupt:
        print("\n❌ 用户中断注册")
    except ValueError:
        print("❌ 输入格式错误，请输入数字")
    except Exception as e:
        print(f"❌ 程序异常: {e}")

if __name__ == "__main__":
    main()
